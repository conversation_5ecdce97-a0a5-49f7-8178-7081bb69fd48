{"version": 3, "file": "preact-router.module.js", "sources": ["../src/util.js", "../src/index.js"], "sourcesContent": ["const EMPTY = {};\n\nexport function assign(obj, props) {\n\t// eslint-disable-next-line guard-for-in\n\tfor (let i in props) {\n\t\tobj[i] = props[i];\n\t}\n\treturn obj;\n}\n\nexport function exec(url, route, opts) {\n\tlet reg = /(?:\\?([^#]*))?(#.*)?$/,\n\t\tc = url.match(reg),\n\t\tmatches = {},\n\t\tret;\n\tif (c && c[1]) {\n\t\tlet p = c[1].split('&');\n\t\tfor (let i = 0; i < p.length; i++) {\n\t\t\tlet r = p[i].split('=');\n\t\t\tmatches[decodeURIComponent(r[0])] = decodeURIComponent(\n\t\t\t\tr.slice(1).join('=')\n\t\t\t);\n\t\t}\n\t}\n\turl = segmentize(url.replace(reg, ''));\n\troute = segmentize(route || '');\n\tlet max = Math.max(url.length, route.length);\n\tfor (let i = 0; i < max; i++) {\n\t\tif (route[i] && route[i].charAt(0) === ':') {\n\t\t\tlet param = route[i].replace(/(^:|[+*?]+$)/g, ''),\n\t\t\t\tflags = (route[i].match(/[+*?]+$/) || EMPTY)[0] || '',\n\t\t\t\tplus = ~flags.indexOf('+'),\n\t\t\t\tstar = ~flags.indexOf('*'),\n\t\t\t\tval = url[i] || '';\n\t\t\tif (!val && !star && (flags.indexOf('?') < 0 || plus)) {\n\t\t\t\tret = false;\n\t\t\t\tbreak;\n\t\t\t}\n\t\t\tmatches[param] = decodeURIComponent(val);\n\t\t\tif (plus || star) {\n\t\t\t\tmatches[param] = url.slice(i).map(decodeURIComponent).join('/');\n\t\t\t\tbreak;\n\t\t\t}\n\t\t} else if (route[i] !== url[i]) {\n\t\t\tret = false;\n\t\t\tbreak;\n\t\t}\n\t}\n\tif (opts.default !== true && ret === false) return false;\n\treturn matches;\n}\n\nexport function pathRankSort(a, b) {\n\treturn a.rank < b.rank ? 1 : a.rank > b.rank ? -1 : a.index - b.index;\n}\n\n// filter out VNodes without attributes (which are unrankeable), and add `index`/`rank` properties to be used in sorting.\nexport function prepareVNodeForRanking(vnode, index) {\n\tvnode.index = index;\n\tvnode.rank = rankChild(vnode);\n\treturn vnode.props;\n}\n\nexport function segmentize(url) {\n\treturn url.replace(/(^\\/+|\\/+$)/g, '').split('/');\n}\n\nexport function rankSegment(segment) {\n\treturn segment.charAt(0) == ':'\n\t\t? 1 + '*+?'.indexOf(segment.charAt(segment.length - 1)) || 4\n\t\t: 5;\n}\n\nexport function rank(path) {\n\treturn segmentize(path).map(rankSegment).join('');\n}\n\nfunction rankChild(vnode) {\n\treturn vnode.props.default ? 0 : rank(vnode.props.path);\n}\n", "import {\n\th,\n\tcloneElement,\n\tComponent,\n\ttoChildArray,\n\tcreateContext\n} from 'preact';\nimport { useContext, useState, useEffect } from 'preact/hooks';\nimport { exec, prepareVNodeForRanking, assign, pathRankSort } from './util';\n\nconst EMPTY = {};\nconst ROUTERS = [];\nconst SUBS = [];\nlet customHistory = null;\n\nconst GLOBAL_ROUTE_CONTEXT = {\n\turl: getCurrentUrl()\n};\n\nconst RouterContext = createContext(GLOBAL_ROUTE_CONTEXT);\n\nfunction useRouter() {\n\tconst ctx = useContext(RouterContext);\n\t// Note: this condition can't change without a remount, so it's a safe conditional hook call\n\tif (ctx === GLOBAL_ROUTE_CONTEXT) {\n\t\t// eslint-disable-next-line react-hooks/rules-of-hooks\n\t\tconst update = useState()[1];\n\t\t// eslint-disable-next-line react-hooks/rules-of-hooks\n\t\tuseEffect(() => {\n\t\t\tSUBS.push(update);\n\t\t\treturn () => SUBS.splice(SUBS.indexOf(update), 1);\n\t\t\t// eslint-disable-next-line react-hooks/exhaustive-deps\n\t\t}, []);\n\t}\n\treturn [ctx, route];\n}\n\nfunction setUrl(url, type = 'push') {\n\tif (customHistory && customHistory[type]) {\n\t\tcustomHistory[type](url);\n\t} else if (typeof history !== 'undefined' && history[`${type}State`]) {\n\t\thistory[`${type}State`](null, null, url);\n\t}\n}\n\nfunction getCurrentUrl() {\n\tlet url;\n\tif (customHistory && customHistory.location) {\n\t\turl = customHistory.location;\n\t} else if (customHistory && customHistory.getCurrentLocation) {\n\t\turl = customHistory.getCurrentLocation();\n\t} else {\n\t\turl = typeof location !== 'undefined' ? location : EMPTY;\n\t}\n\treturn `${url.pathname || ''}${url.search || ''}`;\n}\n\nfunction route(url, replace = false) {\n\tif (typeof url !== 'string' && url.url) {\n\t\treplace = url.replace;\n\t\turl = url.url;\n\t}\n\n\t// only push URL into history if we can handle it\n\tif (canRoute(url)) {\n\t\tsetUrl(url, replace ? 'replace' : 'push');\n\t}\n\n\treturn routeTo(url);\n}\n\n/** Check if the given URL can be handled by any router instances. */\nfunction canRoute(url) {\n\tfor (let i = ROUTERS.length; i--; ) {\n\t\tif (ROUTERS[i].canRoute(url)) return true;\n\t}\n\treturn false;\n}\n\n/** Tell all router instances to handle the given URL.  */\nfunction routeTo(url) {\n\tlet didRoute = false;\n\tfor (let i = 0; i < ROUTERS.length; i++) {\n\t\tif (ROUTERS[i].routeTo(url)) {\n\t\t\tdidRoute = true;\n\t\t}\n\t}\n\treturn didRoute;\n}\n\nfunction routeFromLink(node) {\n\t// only valid elements\n\tif (!node || !node.getAttribute) return;\n\n\tlet href = node.getAttribute('href'),\n\t\ttarget = node.getAttribute('target');\n\n\t// ignore links with targets and non-path URLs\n\tif (!href || !href.match(/^\\//g) || (target && !target.match(/^_?self$/i)))\n\t\treturn;\n\n\t// attempt to route, if no match simply cede control to browser\n\treturn route(href);\n}\n\nfunction prevent(e) {\n\tif (e.stopImmediatePropagation) e.stopImmediatePropagation();\n\tif (e.stopPropagation) e.stopPropagation();\n\te.preventDefault();\n\treturn false;\n}\n\n// Handles both delegated and direct-bound link clicks\nfunction delegateLinkHandler(e) {\n\t// ignore events the browser takes care of already:\n\tif (e.ctrlKey || e.metaKey || e.altKey || e.shiftKey || e.button) return;\n\n\tlet t = e.target;\n\tdo {\n\t\tif (t.localName === 'a' && t.getAttribute('href')) {\n\t\t\tif (t.hasAttribute('data-native') || t.hasAttribute('native')) return;\n\t\t\t// if link is handled by the router, prevent browser defaults\n\t\t\tif (routeFromLink(t)) {\n\t\t\t\treturn prevent(e);\n\t\t\t}\n\t\t}\n\t} while ((t = t.parentNode));\n}\n\nlet eventListenersInitialized = false;\n\nfunction initEventListeners() {\n\tif (eventListenersInitialized) return;\n\teventListenersInitialized = true;\n\n\tif (!customHistory) {\n\t\taddEventListener('popstate', () => {\n\t\t\trouteTo(getCurrentUrl());\n\t\t});\n\t}\n\taddEventListener('click', delegateLinkHandler);\n}\n\n/**\n * @class\n * @this {import('preact').Component}\n */\nfunction Router(props) {\n\tif (props.history) {\n\t\tcustomHistory = props.history;\n\t}\n\n\tthis.state = {\n\t\turl: props.url || getCurrentUrl()\n\t};\n}\n\n// @ts-ignore-next-line\nconst RouterProto = (Router.prototype = new Component());\n\nassign(RouterProto, {\n\tshouldComponentUpdate(props) {\n\t\tif (props.static !== true) return true;\n\t\treturn (\n\t\t\tprops.url !== this.props.url || props.onChange !== this.props.onChange\n\t\t);\n\t},\n\n\t/** Check if the given URL can be matched against any children */\n\tcanRoute(url) {\n\t\tconst children = toChildArray(this.props.children);\n\t\treturn this._getMatchingChild(children, url) !== undefined;\n\t},\n\n\t/** Re-render children with a new URL to match against. */\n\trouteTo(url) {\n\t\tthis.setState({ url });\n\n\t\tconst didRoute = this.canRoute(url);\n\n\t\t// trigger a manual re-route if we're not in the middle of an update:\n\t\tif (!this._updating) this.forceUpdate();\n\n\t\treturn didRoute;\n\t},\n\n\tcomponentWillMount() {\n\t\tthis._updating = true;\n\t},\n\n\tcomponentDidMount() {\n\t\tinitEventListeners();\n\t\tROUTERS.push(this);\n\t\tif (customHistory) {\n\t\t\tthis._unlisten = customHistory.listen(action => {\n\t\t\t\tlet location = action.location || action;\n\t\t\t\tthis.routeTo(`${location.pathname || ''}${location.search || ''}`);\n\t\t\t});\n\t\t}\n\t\tthis._updating = false;\n\t},\n\n\tcomponentWillUnmount() {\n\t\tif (typeof this._unlisten === 'function') this._unlisten();\n\t\tROUTERS.splice(ROUTERS.indexOf(this), 1);\n\t},\n\n\tcomponentWillUpdate() {\n\t\tthis._updating = true;\n\t},\n\n\tcomponentDidUpdate() {\n\t\tthis._updating = false;\n\t},\n\n\t_getMatchingChild(children, url) {\n\t\tchildren = children.filter(prepareVNodeForRanking).sort(pathRankSort);\n\t\tfor (let i = 0; i < children.length; i++) {\n\t\t\tlet vnode = children[i];\n\t\t\tlet matches = exec(url, vnode.props.path, vnode.props);\n\t\t\tif (matches) return [vnode, matches];\n\t\t}\n\t},\n\n\trender({ children, onChange }, { url }) {\n\t\tlet ctx = this._contextValue;\n\n\t\tlet active = this._getMatchingChild(toChildArray(children), url);\n\t\tlet matches, current;\n\t\tif (active) {\n\t\t\tmatches = active[1];\n\t\t\tcurrent = cloneElement(\n\t\t\t\tactive[0],\n\t\t\t\tassign(assign({ url, matches }, matches), {\n\t\t\t\t\tkey: undefined,\n\t\t\t\t\tref: undefined\n\t\t\t\t})\n\t\t\t);\n\t\t}\n\n\t\tif (url !== (ctx && ctx.url)) {\n\t\t\tlet newCtx = {\n\t\t\t\turl,\n\t\t\t\tprevious: ctx && ctx.url,\n\t\t\t\tcurrent,\n\t\t\t\tpath: current ? current.props.path : null,\n\t\t\t\tmatches\n\t\t\t};\n\n\t\t\t// only copy simple properties to the global context:\n\t\t\tassign(GLOBAL_ROUTE_CONTEXT, (ctx = this._contextValue = newCtx));\n\n\t\t\t// these are only available within the subtree of a Router:\n\t\t\tctx.router = this;\n\t\t\tctx.active = current ? [current] : [];\n\n\t\t\t// notify useRouter subscribers outside this subtree:\n\t\t\tfor (let i = SUBS.length; i--; ) SUBS[i]({});\n\n\t\t\tif (typeof onChange === 'function') {\n\t\t\t\tonChange(ctx);\n\t\t\t}\n\t\t}\n\n\t\treturn (\n\t\t\t<RouterContext.Provider value={ctx}>{current}</RouterContext.Provider>\n\t\t);\n\t}\n});\n\nconst Link = props => h('a', assign({ onClick: delegateLinkHandler }, props));\n\nconst Route = props => h(props.component, props);\n\nexport { getCurrentUrl, route, Router, Route, Link, exec, useRouter };\nexport default Router;\n"], "names": ["EMPTY", "assign", "obj", "props", "i", "exec", "url", "route", "opts", "ret", "reg", "c", "match", "matches", "p", "split", "length", "r", "decodeURIComponent", "slice", "join", "segmentize", "replace", "max", "Math", "char<PERSON>t", "param", "flags", "plus", "indexOf", "star", "val", "map", "pathRankSort", "a", "b", "rank", "index", "prepareVNodeForRanking", "vnode", "path", "rankSegment", "rank<PERSON><PERSON>d", "segment", "ROUTERS", "SUBS", "customHistory", "GLOBAL_ROUTE_CONTEXT", "getCurrentUrl", "RouterContext", "createContext", "useRouter", "ctx", "useContext", "update", "useState", "useEffect", "push", "splice", "location", "getCurrentLocation", "pathname", "search", "canRoute", "type", "history", "setUrl", "routeTo", "didRoute", "routeFromLink", "node", "getAttribute", "href", "target", "prevent", "e", "stopImmediatePropagation", "stopPropagation", "preventDefault", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ctrl<PERSON>ey", "metaKey", "altKey", "shift<PERSON>ey", "button", "t", "localName", "hasAttribute", "parentNode", "eventListenersInitialized", "Router", "this", "state", "prototype", "Component", "shouldComponentUpdate", "onChange", "children", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "undefined", "_getMatchingChild", "setState", "_updating", "forceUpdate", "componentWillMount", "componentDidMount", "addEventListener", "_unlisten", "listen", "action", "_this", "componentWillUnmount", "componentWillUpdate", "componentDidUpdate", "filter", "sort", "render", "current", "_contextValue", "active", "cloneElement", "key", "ref", "previous", "router", "Provider", "value", "Link", "h", "onClick", "Route", "component"], "mappings": "wKAAA,IAAMA,EAAQ,YAEEC,EAAOC,EAAKC,GAE3B,IAAK,IAAIC,KAAKD,EACbD,EAAIE,GAAKD,EAAMC,GAEhB,OAAOF,WAGQG,EAAKC,EAAKC,EAAOC,GAChC,IAGCC,EAHGC,EAAM,wBACTC,EAAIL,EAAIM,MAAMF,GACdG,EAAU,GAEX,GAAIF,GAAKA,EAAE,GAEV,IADA,IAAIG,EAAIH,EAAE,GAAGI,MAAM,KACVX,EAAI,EAAGA,EAAIU,EAAEE,OAAQZ,IAAK,CAClC,IAAIa,EAAIH,EAAEV,GAAGW,MAAM,KACnBF,EAAQK,mBAAmBD,EAAE,KAAOC,mBACnCD,EAAEE,MAAM,GAAGC,KAAK,MAInBd,EAAMe,EAAWf,EAAIgB,QAAQZ,EAAK,KAClCH,EAAQc,EAAWd,GAAS,IAE5B,IADA,IAAIgB,EAAMC,KAAKD,IAAIjB,EAAIU,OAAQT,EAAMS,QAC5BZ,EAAI,EAAGA,EAAImB,EAAKnB,IACxB,GAAIG,EAAMH,IAA6B,MAAvBG,EAAMH,GAAGqB,OAAO,GAAY,CAC3C,IAAIC,EAAQnB,EAAMH,GAAGkB,QAAQ,gBAAiB,IAC7CK,GAASpB,EAAMH,GAAGQ,MAAM,YAAcZ,GAAO,IAAM,GACnD4B,GAAQD,EAAME,QAAQ,KACtBC,GAAQH,EAAME,QAAQ,KACtBE,EAAMzB,EAAIF,IAAM,GACjB,IAAK2B,IAAQD,IAASH,EAAME,QAAQ,KAAO,GAAKD,GAAO,CACtDnB,GAAM,EACN,MAGD,GADAI,EAAQa,GAASR,mBAAmBa,GAChCH,GAAQE,EAAM,CACjBjB,EAAQa,GAASpB,EAAIa,MAAMf,GAAG4B,IAAId,oBAAoBE,KAAK,KAC3D,eAESb,EAAMH,KAAOE,EAAIF,GAAI,CAC/BK,GAAM,EACN,MAGF,QAAqB,IAAjBD,YAAiC,IAARC,IACtBI,WAGQoB,EAAaC,EAAGC,GAC/B,OAAOD,EAAEE,KAAOD,EAAEC,KAAO,EAAIF,EAAEE,KAAOD,EAAEC,MAAQ,EAAIF,EAAEG,MAAQF,EAAEE,eAIjDC,EAAuBC,EAAOF,GAG7C,OAFAE,EAAMF,MAAQA,EACdE,EAAMH,KAkBP,SAAmBG,GAClB,OAAOA,EAAMpC,cAAgB,EAJtBkB,EAI+BkB,EAAMpC,MAAMqC,MAJ1BR,IAAIS,GAAarB,KAAK,IAfjCsB,CAAUH,GAChBA,EAAMpC,eAGEkB,EAAWf,GAC1B,OAAOA,EAAIgB,QAAQ,eAAgB,IAAIP,MAAM,cAG9B0B,EAAYE,GAC3B,MAA4B,KAArBA,EAAQlB,OAAO,GACnB,EAAI,MAAMI,QAAQc,EAAQlB,OAAOkB,EAAQ3B,OAAS,KAAO,EACzD,EC5DJ,IAAMhB,EAAQ,GACR4C,EAAU,GACVC,EAAO,GACTC,EAAgB,KAEdC,EAAuB,CAC5BzC,IAAK0C,KAGAC,EAAgBC,EAAcH,GAEpC,SAASI,IACR,IAAMC,EAAMC,EAAWJ,GAEvB,GAAIG,IAAQL,EAAsB,CAEjC,IAAMO,EAASC,IAAW,GAE1BC,EAAU,WAET,OADAX,EAAKY,KAAKH,qBACGT,EAAKa,OAAOb,EAAKhB,QAAQyB,GAAS,KAE7C,IAEJ,MAAO,CAACF,EAAK7C,GAWd,SAASyC,IACR,IAAI1C,EAQJ,WANCA,EADGwC,GAAiBA,EAAca,SAC5Bb,EAAca,SACVb,GAAiBA,EAAcc,mBACnCd,EAAcc,qBAEM,oBAAbD,SAA2BA,SAAW3D,GAEtC6D,UAAY,KAAKvD,EAAIwD,QAAU,IAG9C,SAASvD,EAAMD,EAAKgB,GAWnB,gBAXmBA,IAAAA,GAAU,GACV,iBAARhB,GAAoBA,EAAIA,MAClCgB,EAAUhB,EAAIgB,QACdhB,EAAMA,EAAIA,KAYZ,SAAkBA,GACjB,IAAK,IAAIF,EAAIwC,EAAQ5B,OAAQZ,KAC5B,GAAIwC,EAAQxC,GAAG2D,SAASzD,GAAM,SAE/B,SAZIyD,CAASzD,IA3Bd,SAAgBA,EAAK0D,YAAAA,IAAAA,EAAO,QACvBlB,GAAiBA,EAAckB,GAClClB,EAAckB,GAAM1D,GACS,oBAAZ2D,SAA2BA,QAAWD,YACvDC,QAAWD,WAAa,KAAM,KAAM1D,GAwBpC4D,CAAO5D,EAAKgB,EAAU,UAAY,QAG5B6C,EAAQ7D,GAYhB,SAAS6D,EAAQ7D,GAEhB,IADA,IAAI8D,GAAW,EACNhE,EAAI,EAAGA,EAAIwC,EAAQ5B,OAAQZ,IAC/BwC,EAAQxC,GAAG+D,QAAQ7D,KACtB8D,GAAW,GAGb,OAAOA,EAGR,SAASC,EAAcC,GAEtB,GAAKA,GAASA,EAAKC,aAAnB,CAEA,IAAIC,EAAOF,EAAKC,aAAa,QAC5BE,EAASH,EAAKC,aAAa,UAG5B,GAAKC,GAASA,EAAK5D,MAAM,WAAY6D,GAAWA,EAAO7D,MAAM,cAI7D,OAAOL,EAAMiE,IAGd,SAASE,EAAQC,GAIhB,OAHIA,EAAEC,0BAA0BD,EAAEC,2BAC9BD,EAAEE,iBAAiBF,EAAEE,kBACzBF,EAAEG,oBAKH,SAASC,EAAoBJ,GAE5B,KAAIA,EAAEK,SAAWL,EAAEM,SAAWN,EAAEO,QAAUP,EAAEQ,UAAYR,EAAES,QAA1D,CAEA,IAAIC,EAAIV,EAAEF,OACV,GACC,GAAoB,MAAhBY,EAAEC,WAAqBD,EAAEd,aAAa,QAAS,CAClD,GAAIc,EAAEE,aAAa,gBAAkBF,EAAEE,aAAa,UAAW,OAE/D,GAAIlB,EAAcgB,GACjB,OAAOX,EAAQC,UAGRU,EAAIA,EAAEG,aAGjB,IAAIC,GAA4B,EAkBhC,SAASC,EAAOvF,GACXA,EAAM8D,UACTnB,EAAgB3C,EAAM8D,SAGvB0B,KAAKC,MAAQ,CACZtF,IAAKH,EAAMG,KAAO0C,KAOpB/C,EAFqByF,EAAOG,UAAY,IAAIC,EAExB,CACnBC,+BAAsB5F,GACrB,OAAqB,IAAjBA,UAEHA,EAAMG,MAAQqF,KAAKxF,MAAMG,KAAOH,EAAM6F,WAAaL,KAAKxF,MAAM6F,UAKhEjC,kBAASzD,GACR,IAAM2F,EAAWC,EAAaP,KAAKxF,MAAM8F,UACzC,YAAiDE,SAArCC,EAAkBH,EAAU3F,IAIzC6D,iBAAQ7D,GACPqF,KAAKU,SAAS,CAAE/F,IAAAA,IAEhB,IAAM8D,EAAWuB,KAAK5B,SAASzD,GAK/B,OAFKqF,KAAKW,GAAWX,KAAKY,cAEnBnC,GAGRoC,8BACCb,KAAKW,GAAY,GAGlBG,wCA1DIhB,IACJA,GAA4B,EAEvB3C,GACJ4D,iBAAiB,WAAY,WAC5BvC,EAAQnB,OAGV0D,iBAAiB,QAAS3B,IAoDzBnC,EAAQa,KAAKkC,MACT7C,IACH6C,KAAKgB,EAAY7D,EAAc8D,OAAO,SAAAC,GACrC,IAAIlD,EAAWkD,EAAOlD,UAAYkD,EAClCC,EAAK3C,YAAWR,EAASE,UAAY,KAAKF,EAASG,QAAU,QAG/D6B,KAAKW,GAAY,GAGlBS,gCAC+B,wBAAdJ,GAA0BhB,KAAKgB,IAC/C/D,EAAQc,OAAOd,EAAQf,QAAQ8D,MAAO,IAGvCqB,+BACCrB,KAAKW,GAAY,GAGlBW,8BACCtB,KAAKW,GAAY,GAGlBF,WAAkBH,EAAU3F,GAC3B2F,EAAWA,EAASiB,OAAO5E,GAAwB6E,KAAKlF,GACxD,IAAK,IAAI7B,EAAI,EAAGA,EAAI6F,EAASjF,OAAQZ,IAAK,CACzC,IAAImC,EAAQ0D,EAAS7F,GACjBS,EAAUR,EAAKC,EAAKiC,EAAMpC,MAAMqC,KAAMD,EAAMpC,OAChD,GAAIU,EAAS,MAAO,CAAC0B,EAAO1B,KAI9BuG,yBAIKvG,EAASwG,EAJKrB,IAAAA,SAAc1F,IAAAA,IAC5B8C,EAAMuC,KAAK2B,EAEXC,EAAS5B,KAAKS,EAAkBF,IAH5BD,UAGoD3F,GAa5D,GAXIiH,IAEHF,EAAUG,EACTD,EAAO,GACPtH,EAAOA,EAAO,CAAEK,IAAAA,EAAKO,QAHtBA,EAAU0G,EAAO,IAGgB1G,GAAU,CACzC4G,SAAKtB,EACLuB,SAAKvB,MAKJ7F,KAAS8C,GAAOA,EAAI9C,KAAM,CAU7BL,EAAO8C,EAAuBK,EAAMuC,KAAK2B,EAT5B,CACZhH,IAAAA,EACAqH,SAAUvE,GAAOA,EAAI9C,IACrB+G,QAAAA,EACA7E,KAAM6E,EAAUA,EAAQlH,MAAMqC,KAAO,KACrC3B,QAAAA,IAODuC,EAAIwE,OAASjC,KACbvC,EAAImE,OAASF,EAAU,CAACA,GAAW,GAGnC,IAAK,IAAIjH,EAAIyC,EAAK7B,OAAQZ,KAAOyC,EAAKzC,GAAG,IAEjB,mBAAb4F,GACVA,EAAS5C,GAIX,SACEH,EAAc4E,UAASC,MAAO1E,GAAMiE,MAKlCU,IAAAA,EAAO,SAAA5H,UAAS6H,EAAE,IAAK/H,EAAO,CAAEgI,QAASlD,GAAuB5E,KAEhE+H,EAAQ,SAAA/H,UAAS6H,EAAE7H,EAAMgI,UAAWhI"}