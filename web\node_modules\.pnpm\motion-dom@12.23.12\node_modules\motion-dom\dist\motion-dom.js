!function(t,e){"object"==typeof exports&&"undefined"!=typeof module?e(exports,require("motion-utils")):"function"==typeof define&&define.amd?define(["exports","motion-utils"],e):e((t="undefined"!=typeof globalThis?globalThis:t||self).MotionDom={},t.MotionUtils)}(this,function(t,e){"use strict";const n=["setup","read","resolveKeyframes","preUpdate","update","preRender","render","postRender"],s={value:null,addProjectionMetrics:null};function i(t,i){let r=!1,a=!0;const o={delta:0,timestamp:0,isProcessing:!1},l=()=>r=!0,u=n.reduce((t,e)=>(t[e]=function(t,e){let n=new Set,i=new Set,r=!1,a=!1;const o=new WeakSet;let l={delta:0,timestamp:0,isProcessing:!1},u=0;function c(e){o.has(e)&&(h.schedule(e),t()),u++,e(l)}const h={schedule:(t,e=!1,s=!1)=>{const a=s&&r?n:i;return e&&o.add(t),a.has(t)||a.add(t),t},cancel:t=>{i.delete(t),o.delete(t)},process:t=>{l=t,r?a=!0:(r=!0,[n,i]=[i,n],n.forEach(c),e&&s.value&&s.value.frameloop[e].push(u),u=0,n.clear(),r=!1,a&&(a=!1,h.process(t)))}};return h}(l,i?e:void 0),t),{}),{setup:c,read:h,resolveKeyframes:d,preUpdate:m,update:p,preRender:f,render:g,postRender:y}=u,v=()=>{const n=e.MotionGlobalConfig.useManualTiming?o.timestamp:performance.now();r=!1,e.MotionGlobalConfig.useManualTiming||(o.delta=a?1e3/60:Math.max(Math.min(n-o.timestamp,40),1)),o.timestamp=n,o.isProcessing=!0,c.process(o),h.process(o),d.process(o),m.process(o),p.process(o),f.process(o),g.process(o),y.process(o),o.isProcessing=!1,r&&i&&(a=!1,t(v))};return{schedule:n.reduce((e,n)=>{const s=u[n];return e[n]=(e,n=!1,i=!1)=>(r||(r=!0,a=!0,o.isProcessing||t(v)),s.schedule(e,n,i)),e},{}),cancel:t=>{for(let e=0;e<n.length;e++)u[n[e]].cancel(t)},state:o,steps:u}}const{schedule:r,cancel:a,state:o,steps:l}=i("undefined"!=typeof requestAnimationFrame?requestAnimationFrame:e.noop,!0);let u;function c(){u=void 0}const h={now:()=>(void 0===u&&h.set(o.isProcessing||e.MotionGlobalConfig.useManualTiming?o.timestamp:performance.now()),u),set:t=>{u=t,queueMicrotask(c)}},d={layout:0,mainThread:0,waapi:0},m=t=>e=>"string"==typeof e&&e.startsWith(t),p=m("--"),f=m("var(--"),g=t=>!!f(t)&&y.test(t.split("/*")[0].trim()),y=/var\(--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)$/iu,v={test:t=>"number"==typeof t,parse:parseFloat,transform:t=>t},b={...v,transform:t=>e.clamp(0,1,t)},T={...v,default:1},w=t=>Math.round(1e5*t)/1e5,M=/-?(?:\d+(?:\.\d+)?|\.\d+)/gu;const x=/^(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))$/iu,S=(t,e)=>n=>Boolean("string"==typeof n&&x.test(n)&&n.startsWith(t)||e&&!function(t){return null==t}(n)&&Object.prototype.hasOwnProperty.call(n,e)),A=(t,e,n)=>s=>{if("string"!=typeof s)return s;const[i,r,a,o]=s.match(M);return{[t]:parseFloat(i),[e]:parseFloat(r),[n]:parseFloat(a),alpha:void 0!==o?parseFloat(o):1}},k={...v,transform:t=>Math.round((t=>e.clamp(0,255,t))(t))},E={test:S("rgb","red"),parse:A("red","green","blue"),transform:({red:t,green:e,blue:n,alpha:s=1})=>"rgba("+k.transform(t)+", "+k.transform(e)+", "+k.transform(n)+", "+w(b.transform(s))+")"};const V={test:S("#"),parse:function(t){let e="",n="",s="",i="";return t.length>5?(e=t.substring(1,3),n=t.substring(3,5),s=t.substring(5,7),i=t.substring(7,9)):(e=t.substring(1,2),n=t.substring(2,3),s=t.substring(3,4),i=t.substring(4,5),e+=e,n+=n,s+=s,i+=i),{red:parseInt(e,16),green:parseInt(n,16),blue:parseInt(s,16),alpha:i?parseInt(i,16)/255:1}},transform:E.transform},P=t=>({test:e=>"string"==typeof e&&e.endsWith(t)&&1===e.split(" ").length,parse:parseFloat,transform:e=>`${e}${t}`}),R=P("deg"),D=P("%"),F=P("px"),O=P("vh"),C=P("vw"),K=(()=>({...D,parse:t=>D.parse(t)/100,transform:t=>D.transform(100*t)}))(),L={test:S("hsl","hue"),parse:A("hue","saturation","lightness"),transform:({hue:t,saturation:e,lightness:n,alpha:s=1})=>"hsla("+Math.round(t)+", "+D.transform(w(e))+", "+D.transform(w(n))+", "+w(b.transform(s))+")"},W={test:t=>E.test(t)||V.test(t)||L.test(t),parse:t=>E.test(t)?E.parse(t):L.test(t)?L.parse(t):V.parse(t),transform:t=>"string"==typeof t?t:t.hasOwnProperty("red")?E.transform(t):L.transform(t),getAnimatableNone:t=>{const e=W.parse(t);return e.alpha=0,W.transform(e)}},B=/(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))/giu;const $="number",N="color",j=/var\s*\(\s*--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)|#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\)|-?(?:\d+(?:\.\d+)?|\.\d+)/giu;function I(t){const e=t.toString(),n=[],s={color:[],number:[],var:[]},i=[];let r=0;const a=e.replace(j,t=>(W.test(t)?(s.color.push(r),i.push(N),n.push(W.parse(t))):t.startsWith("var(")?(s.var.push(r),i.push("var"),n.push(t)):(s.number.push(r),i.push($),n.push(parseFloat(t))),++r,"${}")).split("${}");return{values:n,split:a,indexes:s,types:i}}function Y(t){return I(t).values}function z(t){const{split:e,types:n}=I(t),s=e.length;return t=>{let i="";for(let r=0;r<s;r++)if(i+=e[r],void 0!==t[r]){const e=n[r];i+=e===$?w(t[r]):e===N?W.transform(t[r]):t[r]}return i}}const X=t=>"number"==typeof t?0:W.test(t)?W.getAnimatableNone(t):t;const G={test:function(t){return isNaN(t)&&"string"==typeof t&&(t.match(M)?.length||0)+(t.match(B)?.length||0)>0},parse:Y,createTransformer:z,getAnimatableNone:function(t){const e=Y(t);return z(t)(e.map(X))}};function U(t,e,n){return n<0&&(n+=1),n>1&&(n-=1),n<1/6?t+6*(e-t)*n:n<.5?e:n<2/3?t+(e-t)*(2/3-n)*6:t}function q({hue:t,saturation:e,lightness:n,alpha:s}){t/=360,n/=100;let i=0,r=0,a=0;if(e/=100){const s=n<.5?n*(1+e):n+e-n*e,o=2*n-s;i=U(o,s,t+1/3),r=U(o,s,t),a=U(o,s,t-1/3)}else i=r=a=n;return{red:Math.round(255*i),green:Math.round(255*r),blue:Math.round(255*a),alpha:s}}function Z(t,e){return n=>n>0?e:t}const _=(t,e,n)=>t+(e-t)*n,H=(t,e,n)=>{const s=t*t,i=n*(e*e-s)+s;return i<0?0:Math.sqrt(i)},J=[V,E,L];function Q(t){const n=(s=t,J.find(t=>t.test(s)));var s;if(e.warning(Boolean(n),`'${t}' is not an animatable color. Use the equivalent color code instead.`,"color-not-animatable"),!Boolean(n))return!1;let i=n.parse(t);return n===L&&(i=q(i)),i}const tt=(t,e)=>{const n=Q(t),s=Q(e);if(!n||!s)return Z(t,e);const i={...n};return t=>(i.red=H(n.red,s.red,t),i.green=H(n.green,s.green,t),i.blue=H(n.blue,s.blue,t),i.alpha=_(n.alpha,s.alpha,t),E.transform(i))},et=new Set(["none","hidden"]);function nt(t,e){return et.has(t)?n=>n<=0?t:e:n=>n>=1?e:t}function st(t,e){return n=>_(t,e,n)}function it(t){return"number"==typeof t?st:"string"==typeof t?g(t)?Z:W.test(t)?tt:ot:Array.isArray(t)?rt:"object"==typeof t?W.test(t)?tt:at:Z}function rt(t,e){const n=[...t],s=n.length,i=t.map((t,n)=>it(t)(t,e[n]));return t=>{for(let e=0;e<s;e++)n[e]=i[e](t);return n}}function at(t,e){const n={...t,...e},s={};for(const i in n)void 0!==t[i]&&void 0!==e[i]&&(s[i]=it(t[i])(t[i],e[i]));return t=>{for(const e in s)n[e]=s[e](t);return n}}const ot=(t,n)=>{const s=G.createTransformer(n),i=I(t),r=I(n);return i.indexes.var.length===r.indexes.var.length&&i.indexes.color.length===r.indexes.color.length&&i.indexes.number.length>=r.indexes.number.length?et.has(t)&&!r.values.length||et.has(n)&&!i.values.length?nt(t,n):e.pipe(rt(function(t,e){const n=[],s={color:0,var:0,number:0};for(let i=0;i<e.values.length;i++){const r=e.types[i],a=t.indexes[r][s[r]],o=t.values[a]??0;n[i]=o,s[r]++}return n}(i,r),r.values),s):(e.warning(!0,`Complex values '${t}' and '${n}' too different to mix. Ensure all colors are of the same type, and that each contains the same quantity of number and color values. Falling back to instant transition.`,"complex-values-different"),Z(t,n))};function lt(t,e,n){if("number"==typeof t&&"number"==typeof e&&"number"==typeof n)return _(t,e,n);return it(t)(t,e)}const ut=t=>{const e=({timestamp:e})=>t(e);return{start:(t=!0)=>r.update(e,t),stop:()=>a(e),now:()=>o.isProcessing?o.timestamp:h.now()}},ct=(t,e,n=10)=>{let s="";const i=Math.max(Math.round(e/n),2);for(let e=0;e<i;e++)s+=Math.round(1e4*t(e/(i-1)))/1e4+", ";return`linear(${s.substring(0,s.length-2)})`},ht=2e4;function dt(t){let e=0;let n=t.next(e);for(;!n.done&&e<ht;)e+=50,n=t.next(e);return e>=ht?1/0:e}function mt(t,n=100,s){const i=s({...t,keyframes:[0,n]}),r=Math.min(dt(i),ht);return{type:"keyframes",ease:t=>i.next(r*t).value/n,duration:e.millisecondsToSeconds(r)}}function pt(t,n,s){const i=Math.max(n-5,0);return e.velocityPerSecond(s-t(i),n-i)}const ft=100,gt=10,yt=1,vt=0,bt=800,Tt=.3,wt=.3,Mt={granular:.01,default:2},xt={granular:.005,default:.5},St=.01,At=10,kt=.05,Et=1,Vt=.001;function Pt({duration:t=bt,bounce:n=Tt,velocity:s=vt,mass:i=yt}){let r,a;e.warning(t<=e.secondsToMilliseconds(At),"Spring duration must be 10 seconds or less","spring-duration-limit");let o=1-n;o=e.clamp(kt,Et,o),t=e.clamp(St,At,e.millisecondsToSeconds(t)),o<1?(r=e=>{const n=e*o,i=n*t,r=n-s,a=Dt(e,o),l=Math.exp(-i);return Vt-r/a*l},a=e=>{const n=e*o*t,i=n*s+s,a=Math.pow(o,2)*Math.pow(e,2)*t,l=Math.exp(-n),u=Dt(Math.pow(e,2),o);return(-r(e)+Vt>0?-1:1)*((i-a)*l)/u}):(r=e=>Math.exp(-e*t)*((e-s)*t+1)-.001,a=e=>Math.exp(-e*t)*(t*t*(s-e)));const l=function(t,e,n){let s=n;for(let n=1;n<Rt;n++)s-=t(s)/e(s);return s}(r,a,5/t);if(t=e.secondsToMilliseconds(t),isNaN(l))return{stiffness:ft,damping:gt,duration:t};{const e=Math.pow(l,2)*i;return{stiffness:e,damping:2*o*Math.sqrt(i*e),duration:t}}}const Rt=12;function Dt(t,e){return t*Math.sqrt(1-e*e)}const Ft=["duration","bounce"],Ot=["stiffness","damping","mass"];function Ct(t,e){return e.some(e=>void 0!==t[e])}function Kt(t=wt,n=Tt){const s="object"!=typeof t?{visualDuration:t,keyframes:[0,1],bounce:n}:t;let{restSpeed:i,restDelta:r}=s;const a=s.keyframes[0],o=s.keyframes[s.keyframes.length-1],l={done:!1,value:a},{stiffness:u,damping:c,mass:h,duration:d,velocity:m,isResolvedFromDuration:p}=function(t){let n={velocity:vt,stiffness:ft,damping:gt,mass:yt,isResolvedFromDuration:!1,...t};if(!Ct(t,Ot)&&Ct(t,Ft))if(t.visualDuration){const s=t.visualDuration,i=2*Math.PI/(1.2*s),r=i*i,a=2*e.clamp(.05,1,1-(t.bounce||0))*Math.sqrt(r);n={...n,mass:yt,stiffness:r,damping:a}}else{const e=Pt(t);n={...n,...e,mass:yt},n.isResolvedFromDuration=!0}return n}({...s,velocity:-e.millisecondsToSeconds(s.velocity||0)}),f=m||0,g=c/(2*Math.sqrt(u*h)),y=o-a,v=e.millisecondsToSeconds(Math.sqrt(u/h)),b=Math.abs(y)<5;let T;if(i||(i=b?Mt.granular:Mt.default),r||(r=b?xt.granular:xt.default),g<1){const t=Dt(v,g);T=e=>{const n=Math.exp(-g*v*e);return o-n*((f+g*v*y)/t*Math.sin(t*e)+y*Math.cos(t*e))}}else if(1===g)T=t=>o-Math.exp(-v*t)*(y+(f+v*y)*t);else{const t=v*Math.sqrt(g*g-1);T=e=>{const n=Math.exp(-g*v*e),s=Math.min(t*e,300);return o-n*((f+g*v*y)*Math.sinh(s)+t*y*Math.cosh(s))/t}}const w={calculatedDuration:p&&d||null,next:t=>{const n=T(t);if(p)l.done=t>=d;else{let s=0===t?f:0;g<1&&(s=0===t?e.secondsToMilliseconds(f):pt(T,t,n));const a=Math.abs(s)<=i,u=Math.abs(o-n)<=r;l.done=a&&u}return l.value=l.done?o:n,l},toString:()=>{const t=Math.min(dt(w),ht),e=ct(e=>w.next(t*e).value,t,30);return t+"ms "+e},toTransition:()=>{}};return w}function Lt({keyframes:t,velocity:e=0,power:n=.8,timeConstant:s=325,bounceDamping:i=10,bounceStiffness:r=500,modifyTarget:a,min:o,max:l,restDelta:u=.5,restSpeed:c}){const h=t[0],d={done:!1,value:h},m=t=>void 0===o?l:void 0===l||Math.abs(o-t)<Math.abs(l-t)?o:l;let p=n*e;const f=h+p,g=void 0===a?f:a(f);g!==f&&(p=g-h);const y=t=>-p*Math.exp(-t/s),v=t=>g+y(t),b=t=>{const e=y(t),n=v(t);d.done=Math.abs(e)<=u,d.value=d.done?g:n};let T,w;const M=t=>{var e;(e=d.value,void 0!==o&&e<o||void 0!==l&&e>l)&&(T=t,w=Kt({keyframes:[d.value,m(d.value)],velocity:pt(v,t,d.value),damping:i,stiffness:r,restDelta:u,restSpeed:c}))};return M(0),{calculatedDuration:null,next:t=>{let e=!1;return w||void 0!==T||(e=!0,b(t),M(t)),void 0!==T&&t>=T?w.next(t-T):(!e&&b(t),d)}}}function Wt(t,n,{clamp:s=!0,ease:i,mixer:r}={}){const a=t.length;if(e.invariant(a===n.length,"Both input and output ranges must be the same length","range-length"),1===a)return()=>n[0];if(2===a&&n[0]===n[1])return()=>n[1];const o=t[0]===t[1];t[0]>t[a-1]&&(t=[...t].reverse(),n=[...n].reverse());const l=function(t,n,s){const i=[],r=s||e.MotionGlobalConfig.mix||lt,a=t.length-1;for(let s=0;s<a;s++){let a=r(t[s],t[s+1]);if(n){const t=Array.isArray(n)?n[s]||e.noop:n;a=e.pipe(t,a)}i.push(a)}return i}(n,i,r),u=l.length,c=s=>{if(o&&s<t[0])return n[0];let i=0;if(u>1)for(;i<t.length-2&&!(s<t[i+1]);i++);const r=e.progress(t[i],t[i+1],s);return l[i](r)};return s?n=>c(e.clamp(t[0],t[a-1],n)):c}function Bt(t,n){const s=t[t.length-1];for(let i=1;i<=n;i++){const r=e.progress(0,n,i);t.push(_(s,1,r))}}function $t(t){const e=[0];return Bt(e,t.length-1),e}function Nt(t,e){return t.map(t=>t*e)}function jt(t,n){return t.map(()=>n||e.easeInOut).splice(0,t.length-1)}function It({duration:t=300,keyframes:n,times:s,ease:i="easeInOut"}){const r=e.isEasingArray(i)?i.map(e.easingDefinitionToFunction):e.easingDefinitionToFunction(i),a={done:!1,value:n[0]},o=Wt(Nt(s&&s.length===n.length?s:$t(n),t),n,{ease:Array.isArray(r)?r:jt(n,r)});return{calculatedDuration:t,next:e=>(a.value=o(e),a.done=e>=t,a)}}Kt.applyToOptions=t=>{const n=mt(t,100,Kt);return t.ease=n.ease,t.duration=e.secondsToMilliseconds(n.duration),t.type="keyframes",t};const Yt=t=>null!==t;function zt(t,{repeat:e,repeatType:n="loop"},s,i=1){const r=t.filter(Yt),a=i<0||e&&"loop"!==n&&e%2==1?0:r.length-1;return a&&void 0!==s?s:r[a]}const Xt={decay:Lt,inertia:Lt,tween:It,keyframes:It,spring:Kt};function Gt(t){"string"==typeof t.type&&(t.type=Xt[t.type])}class Ut{constructor(){this.updateFinished()}get finished(){return this._finished}updateFinished(){this._finished=new Promise(t=>{this.resolve=t})}notifyFinished(){this.resolve()}then(t,e){return this.finished.then(t,e)}}const qt=t=>t/100;class Zt extends Ut{constructor(t){super(),this.state="idle",this.startTime=null,this.isStopped=!1,this.currentTime=0,this.holdTime=null,this.playbackSpeed=1,this.stop=()=>{const{motionValue:t}=this.options;t&&t.updatedAt!==h.now()&&this.tick(h.now()),this.isStopped=!0,"idle"!==this.state&&(this.teardown(),this.options.onStop?.())},d.mainThread++,this.options=t,this.initAnimation(),this.play(),!1===t.autoplay&&this.pause()}initAnimation(){const{options:t}=this;Gt(t);const{type:n=It,repeat:s=0,repeatDelay:i=0,repeatType:r,velocity:a=0}=t;let{keyframes:o}=t;const l=n||It;l!==It&&"number"!=typeof o[0]&&(this.mixKeyframes=e.pipe(qt,lt(o[0],o[1])),o=[0,100]);const u=l({...t,keyframes:o});"mirror"===r&&(this.mirroredGenerator=l({...t,keyframes:[...o].reverse(),velocity:-a})),null===u.calculatedDuration&&(u.calculatedDuration=dt(u));const{calculatedDuration:c}=u;this.calculatedDuration=c,this.resolvedDuration=c+i,this.totalDuration=this.resolvedDuration*(s+1)-i,this.generator=u}updateTime(t){const e=Math.round(t-this.startTime)*this.playbackSpeed;null!==this.holdTime?this.currentTime=this.holdTime:this.currentTime=e}tick(t,n=!1){const{generator:s,totalDuration:i,mixKeyframes:r,mirroredGenerator:a,resolvedDuration:o,calculatedDuration:l}=this;if(null===this.startTime)return s.next(0);const{delay:u=0,keyframes:c,repeat:h,repeatType:d,repeatDelay:m,type:p,onUpdate:f,finalKeyframe:g}=this.options;this.speed>0?this.startTime=Math.min(this.startTime,t):this.speed<0&&(this.startTime=Math.min(t-i/this.speed,this.startTime)),n?this.currentTime=t:this.updateTime(t);const y=this.currentTime-u*(this.playbackSpeed>=0?1:-1),v=this.playbackSpeed>=0?y<0:y>i;this.currentTime=Math.max(y,0),"finished"===this.state&&null===this.holdTime&&(this.currentTime=i);let b=this.currentTime,T=s;if(h){const t=Math.min(this.currentTime,i)/o;let n=Math.floor(t),s=t%1;!s&&t>=1&&(s=1),1===s&&n--,n=Math.min(n,h+1);Boolean(n%2)&&("reverse"===d?(s=1-s,m&&(s-=m/o)):"mirror"===d&&(T=a)),b=e.clamp(0,1,s)*o}const w=v?{done:!1,value:c[0]}:T.next(b);r&&(w.value=r(w.value));let{done:M}=w;v||null===l||(M=this.playbackSpeed>=0?this.currentTime>=i:this.currentTime<=0);const x=null===this.holdTime&&("finished"===this.state||"running"===this.state&&M);return x&&p!==Lt&&(w.value=zt(c,this.options,g,this.speed)),f&&f(w.value),x&&this.finish(),w}then(t,e){return this.finished.then(t,e)}get duration(){return e.millisecondsToSeconds(this.calculatedDuration)}get time(){return e.millisecondsToSeconds(this.currentTime)}set time(t){t=e.secondsToMilliseconds(t),this.currentTime=t,null===this.startTime||null!==this.holdTime||0===this.playbackSpeed?this.holdTime=t:this.driver&&(this.startTime=this.driver.now()-t/this.playbackSpeed),this.driver?.start(!1)}get speed(){return this.playbackSpeed}set speed(t){this.updateTime(h.now());const n=this.playbackSpeed!==t;this.playbackSpeed=t,n&&(this.time=e.millisecondsToSeconds(this.currentTime))}play(){if(this.isStopped)return;const{driver:t=ut,startTime:e}=this.options;this.driver||(this.driver=t(t=>this.tick(t))),this.options.onPlay?.();const n=this.driver.now();"finished"===this.state?(this.updateFinished(),this.startTime=n):null!==this.holdTime?this.startTime=n-this.holdTime:this.startTime||(this.startTime=e??n),"finished"===this.state&&this.speed<0&&(this.startTime+=this.calculatedDuration),this.holdTime=null,this.state="running",this.driver.start()}pause(){this.state="paused",this.updateTime(h.now()),this.holdTime=this.currentTime}complete(){"running"!==this.state&&this.play(),this.state="finished",this.holdTime=null}finish(){this.notifyFinished(),this.teardown(),this.state="finished",this.options.onComplete?.()}cancel(){this.holdTime=null,this.startTime=0,this.tick(0),this.teardown(),this.options.onCancel?.()}teardown(){this.state="idle",this.stopDriver(),this.startTime=this.holdTime=null,d.mainThread--}stopDriver(){this.driver&&(this.driver.stop(),this.driver=void 0)}sample(t){return this.startTime=0,this.tick(t,!0)}attachTimeline(t){return this.options.allowFlatten&&(this.options.type="keyframes",this.options.ease="linear",this.initAnimation()),this.driver?.stop(),t.observe(this)}}function _t(t){for(let e=1;e<t.length;e++)t[e]??(t[e]=t[e-1])}const Ht=t=>180*t/Math.PI,Jt=t=>{const e=Ht(Math.atan2(t[1],t[0]));return te(e)},Qt={x:4,y:5,translateX:4,translateY:5,scaleX:0,scaleY:3,scale:t=>(Math.abs(t[0])+Math.abs(t[3]))/2,rotate:Jt,rotateZ:Jt,skewX:t=>Ht(Math.atan(t[1])),skewY:t=>Ht(Math.atan(t[2])),skew:t=>(Math.abs(t[1])+Math.abs(t[2]))/2},te=t=>((t%=360)<0&&(t+=360),t),ee=t=>Math.sqrt(t[0]*t[0]+t[1]*t[1]),ne=t=>Math.sqrt(t[4]*t[4]+t[5]*t[5]),se={x:12,y:13,z:14,translateX:12,translateY:13,translateZ:14,scaleX:ee,scaleY:ne,scale:t=>(ee(t)+ne(t))/2,rotateX:t=>te(Ht(Math.atan2(t[6],t[5]))),rotateY:t=>te(Ht(Math.atan2(-t[2],t[0]))),rotateZ:Jt,rotate:Jt,skewX:t=>Ht(Math.atan(t[4])),skewY:t=>Ht(Math.atan(t[1])),skew:t=>(Math.abs(t[1])+Math.abs(t[4]))/2};function ie(t){return t.includes("scale")?1:0}function re(t,e){if(!t||"none"===t)return ie(e);const n=t.match(/^matrix3d\(([-\d.e\s,]+)\)$/u);let s,i;if(n)s=se,i=n;else{const e=t.match(/^matrix\(([-\d.e\s,]+)\)$/u);s=Qt,i=e}if(!i)return ie(e);const r=s[e],a=i[1].split(",").map(ae);return"function"==typeof r?r(a):a[r]}function ae(t){return parseFloat(t.trim())}const oe=["transformPerspective","x","y","z","translateX","translateY","translateZ","scale","scaleX","scaleY","rotate","rotateX","rotateY","rotateZ","skew","skewX","skewY"],le=(()=>new Set(oe))(),ue=t=>t===v||t===F,ce=new Set(["x","y","z"]),he=oe.filter(t=>!ce.has(t));const de={width:({x:t},{paddingLeft:e="0",paddingRight:n="0"})=>t.max-t.min-parseFloat(e)-parseFloat(n),height:({y:t},{paddingTop:e="0",paddingBottom:n="0"})=>t.max-t.min-parseFloat(e)-parseFloat(n),top:(t,{top:e})=>parseFloat(e),left:(t,{left:e})=>parseFloat(e),bottom:({y:t},{top:e})=>parseFloat(e)+(t.max-t.min),right:({x:t},{left:e})=>parseFloat(e)+(t.max-t.min),x:(t,{transform:e})=>re(e,"x"),y:(t,{transform:e})=>re(e,"y")};de.translateX=de.x,de.translateY=de.y;const me=new Set;let pe=!1,fe=!1,ge=!1;function ye(){if(fe){const t=Array.from(me).filter(t=>t.needsMeasurement),e=new Set(t.map(t=>t.element)),n=new Map;e.forEach(t=>{const e=function(t){const e=[];return he.forEach(n=>{const s=t.getValue(n);void 0!==s&&(e.push([n,s.get()]),s.set(n.startsWith("scale")?1:0))}),e}(t);e.length&&(n.set(t,e),t.render())}),t.forEach(t=>t.measureInitialState()),e.forEach(t=>{t.render();const e=n.get(t);e&&e.forEach(([e,n])=>{t.getValue(e)?.set(n)})}),t.forEach(t=>t.measureEndState()),t.forEach(t=>{void 0!==t.suspendedScrollY&&window.scrollTo(0,t.suspendedScrollY)})}fe=!1,pe=!1,me.forEach(t=>t.complete(ge)),me.clear()}function ve(){me.forEach(t=>{t.readKeyframes(),t.needsMeasurement&&(fe=!0)})}function be(){ge=!0,ve(),ye(),ge=!1}class Te{constructor(t,e,n,s,i,r=!1){this.state="pending",this.isAsync=!1,this.needsMeasurement=!1,this.unresolvedKeyframes=[...t],this.onComplete=e,this.name=n,this.motionValue=s,this.element=i,this.isAsync=r}scheduleResolve(){this.state="scheduled",this.isAsync?(me.add(this),pe||(pe=!0,r.read(ve),r.resolveKeyframes(ye))):(this.readKeyframes(),this.complete())}readKeyframes(){const{unresolvedKeyframes:t,name:e,element:n,motionValue:s}=this;if(null===t[0]){const i=s?.get(),r=t[t.length-1];if(void 0!==i)t[0]=i;else if(n&&e){const s=n.readValue(e,r);null!=s&&(t[0]=s)}void 0===t[0]&&(t[0]=r),s&&void 0===i&&s.set(t[0])}_t(t)}setFinalKeyframe(){}measureInitialState(){}renderEndStyles(){}measureEndState(){}complete(t=!1){this.state="complete",this.onComplete(this.unresolvedKeyframes,this.finalKeyframe,t),me.delete(this)}cancel(){"scheduled"===this.state&&(me.delete(this),this.state="pending")}resume(){"pending"===this.state&&this.scheduleResolve()}}const we=t=>t.startsWith("--");function Me(t,e,n){we(e)?t.style.setProperty(e,n):t.style[e]=n}const xe=e.memo(()=>void 0!==window.ScrollTimeline),Se={};function Ae(t,n){const s=e.memo(t);return()=>Se[n]??s()}const ke=Ae(()=>{try{document.createElement("div").animate({opacity:0},{easing:"linear(0, 1)"})}catch(t){return!1}return!0},"linearEasing"),Ee=([t,e,n,s])=>`cubic-bezier(${t}, ${e}, ${n}, ${s})`,Ve={linear:"linear",ease:"ease",easeIn:"ease-in",easeOut:"ease-out",easeInOut:"ease-in-out",circIn:Ee([0,.65,.55,1]),circOut:Ee([.55,0,1,.45]),backIn:Ee([.31,.01,.66,-.59]),backOut:Ee([.33,1.53,.69,.99])};function Pe(t,n){return t?"function"==typeof t?ke()?ct(t,n):"ease-out":e.isBezierDefinition(t)?Ee(t):Array.isArray(t)?t.map(t=>Pe(t,n)||Ve.easeOut):Ve[t]:void 0}function Re(t,e,n,{delay:i=0,duration:r=300,repeat:a=0,repeatType:o="loop",ease:l="easeOut",times:u}={},c=void 0){const h={[e]:n};u&&(h.offset=u);const m=Pe(l,r);Array.isArray(m)&&(h.easing=m),s.value&&d.waapi++;const p={delay:i,duration:r,easing:Array.isArray(m)?"linear":m,fill:"both",iterations:a+1,direction:"reverse"===o?"alternate":"normal"};c&&(p.pseudoElement=c);const f=t.animate(h,p);return s.value&&f.finished.finally(()=>{d.waapi--}),f}function De(t){return"function"==typeof t&&"applyToOptions"in t}function Fe({type:t,...e}){return De(t)&&ke()?t.applyToOptions(e):(e.duration??(e.duration=300),e.ease??(e.ease="easeOut"),e)}class Oe extends Ut{constructor(t){if(super(),this.finishedTime=null,this.isStopped=!1,!t)return;const{element:n,name:s,keyframes:i,pseudoElement:r,allowFlatten:a=!1,finalKeyframe:o,onComplete:l}=t;this.isPseudoElement=Boolean(r),this.allowFlatten=a,this.options=t,e.invariant("string"!=typeof t.type,'Mini animate() doesn\'t support "type" as a string.',"mini-spring");const u=Fe(t);this.animation=Re(n,s,i,u,r),!1===u.autoplay&&this.animation.pause(),this.animation.onfinish=()=>{if(this.finishedTime=this.time,!r){const t=zt(i,this.options,o,this.speed);this.updateMotionValue?this.updateMotionValue(t):Me(n,s,t),this.animation.cancel()}l?.(),this.notifyFinished()}}play(){this.isStopped||(this.animation.play(),"finished"===this.state&&this.updateFinished())}pause(){this.animation.pause()}complete(){this.animation.finish?.()}cancel(){try{this.animation.cancel()}catch(t){}}stop(){if(this.isStopped)return;this.isStopped=!0;const{state:t}=this;"idle"!==t&&"finished"!==t&&(this.updateMotionValue?this.updateMotionValue():this.commitStyles(),this.isPseudoElement||this.cancel())}commitStyles(){this.isPseudoElement||this.animation.commitStyles?.()}get duration(){const t=this.animation.effect?.getComputedTiming?.().duration||0;return e.millisecondsToSeconds(Number(t))}get time(){return e.millisecondsToSeconds(Number(this.animation.currentTime)||0)}set time(t){this.finishedTime=null,this.animation.currentTime=e.secondsToMilliseconds(t)}get speed(){return this.animation.playbackRate}set speed(t){t<0&&(this.finishedTime=null),this.animation.playbackRate=t}get state(){return null!==this.finishedTime?"finished":this.animation.playState}get startTime(){return Number(this.animation.startTime)}set startTime(t){this.animation.startTime=t}attachTimeline({timeline:t,observe:n}){return this.allowFlatten&&this.animation.effect?.updateTiming({easing:"linear"}),this.animation.onfinish=null,t&&xe()?(this.animation.timeline=t,e.noop):n(this)}}const Ce={anticipate:e.anticipate,backInOut:e.backInOut,circInOut:e.circInOut};function Ke(t){"string"==typeof t.ease&&t.ease in Ce&&(t.ease=Ce[t.ease])}class Le extends Oe{constructor(t){Ke(t),Gt(t),super(t),t.startTime&&(this.startTime=t.startTime),this.options=t}updateMotionValue(t){const{motionValue:n,onUpdate:s,onComplete:i,element:r,...a}=this.options;if(!n)return;if(void 0!==t)return void n.set(t);const o=new Zt({...a,autoplay:!1}),l=e.secondsToMilliseconds(this.finishedTime??this.time);n.setWithVelocity(o.sample(l-10).value,o.sample(l).value,10),o.stop()}}const We=(t,e)=>"zIndex"!==e&&(!("number"!=typeof t&&!Array.isArray(t))||!("string"!=typeof t||!G.test(t)&&"0"!==t||t.startsWith("url(")));function Be(t){t.duration=0,t.type}const $e=new Set(["opacity","clipPath","filter","transform"]),Ne=e.memo(()=>Object.hasOwnProperty.call(Element.prototype,"animate"));function je(t){const{motionValue:e,name:n,repeatDelay:s,repeatType:i,damping:r,type:a}=t,o=e?.owner?.current;if(!(o instanceof HTMLElement))return!1;const{onUpdate:l,transformTemplate:u}=e.owner.getProps();return Ne()&&n&&$e.has(n)&&("transform"!==n||!u)&&!l&&!s&&"mirror"!==i&&0!==r&&"inertia"!==a}class Ie{constructor(t){this.stop=()=>this.runAll("stop"),this.animations=t.filter(Boolean)}get finished(){return Promise.all(this.animations.map(t=>t.finished))}getAll(t){return this.animations[0][t]}setAll(t,e){for(let n=0;n<this.animations.length;n++)this.animations[n][t]=e}attachTimeline(t){const e=this.animations.map(e=>e.attachTimeline(t));return()=>{e.forEach((t,e)=>{t&&t(),this.animations[e].stop()})}}get time(){return this.getAll("time")}set time(t){this.setAll("time",t)}get speed(){return this.getAll("speed")}set speed(t){this.setAll("speed",t)}get state(){return this.getAll("state")}get startTime(){return this.getAll("startTime")}get duration(){let t=0;for(let e=0;e<this.animations.length;e++)t=Math.max(t,this.animations[e].duration);return t}runAll(t){this.animations.forEach(e=>e[t]())}play(){this.runAll("play")}pause(){this.runAll("pause")}cancel(){this.runAll("cancel")}complete(){this.runAll("complete")}}class Ye extends Oe{constructor(t){super(),this.animation=t,t.onfinish=()=>{this.finishedTime=this.time,this.notifyFinished()}}}const ze=new WeakMap;const Xe=/^var\(--(?:([\w-]+)|([\w-]+), ?([a-zA-Z\d ()%#.,-]+))\)/u;function Ge(t){const e=Xe.exec(t);if(!e)return[,];const[,n,s,i]=e;return[`--${n??s}`,i]}function Ue(t,n,s=1){e.invariant(s<=4,`Max CSS variable fallback depth detected in property "${t}". This may indicate a circular fallback dependency.`,"max-css-var-depth");const[i,r]=Ge(t);if(!i)return;const a=window.getComputedStyle(n).getPropertyValue(i);if(a){const t=a.trim();return e.isNumericalString(t)?parseFloat(t):t}return g(r)?Ue(r,n,s+1):r}function qe(t,e){return t?.[e]??t?.default??t}const Ze=new Set(["width","height","top","left","right","bottom",...oe]),_e=t=>e=>e.test(t),He=[v,F,D,R,C,O,{test:t=>"auto"===t,parse:t=>t}],Je=t=>He.find(_e(t));function Qe(t){return"number"==typeof t?0===t:null===t||("none"===t||"0"===t||e.isZeroValueString(t))}const tn=new Set(["brightness","contrast","saturate","opacity"]);function en(t){const[e,n]=t.slice(0,-1).split("(");if("drop-shadow"===e)return t;const[s]=n.match(M)||[];if(!s)return t;const i=n.replace(s,"");let r=tn.has(e)?1:0;return s!==n&&(r*=100),e+"("+r+i+")"}const nn=/\b([a-z-]*)\(.*?\)/gu,sn={...G,getAnimatableNone:t=>{const e=t.match(nn);return e?e.map(en).join(" "):t}},rn={...v,transform:Math.round},an={rotate:R,rotateX:R,rotateY:R,rotateZ:R,scale:T,scaleX:T,scaleY:T,scaleZ:T,skew:R,skewX:R,skewY:R,distance:F,translateX:F,translateY:F,translateZ:F,x:F,y:F,z:F,perspective:F,transformPerspective:F,opacity:b,originX:K,originY:K,originZ:F},on={borderWidth:F,borderTopWidth:F,borderRightWidth:F,borderBottomWidth:F,borderLeftWidth:F,borderRadius:F,radius:F,borderTopLeftRadius:F,borderTopRightRadius:F,borderBottomRightRadius:F,borderBottomLeftRadius:F,width:F,maxWidth:F,height:F,maxHeight:F,top:F,right:F,bottom:F,left:F,padding:F,paddingTop:F,paddingRight:F,paddingBottom:F,paddingLeft:F,margin:F,marginTop:F,marginRight:F,marginBottom:F,marginLeft:F,backgroundPositionX:F,backgroundPositionY:F,...an,zIndex:rn,fillOpacity:b,strokeOpacity:b,numOctaves:rn},ln={...on,color:W,backgroundColor:W,outlineColor:W,fill:W,stroke:W,borderColor:W,borderTopColor:W,borderRightColor:W,borderBottomColor:W,borderLeftColor:W,filter:sn,WebkitFilter:sn},un=t=>ln[t];function cn(t,e){let n=un(t);return n!==sn&&(n=G),n.getAnimatableNone?n.getAnimatableNone(e):void 0}const hn=new Set(["auto","none","0"]);const dn=new Set(["borderWidth","borderTopWidth","borderRightWidth","borderBottomWidth","borderLeftWidth","borderRadius","radius","borderTopLeftRadius","borderTopRightRadius","borderBottomRightRadius","borderBottomLeftRadius","width","maxWidth","height","maxHeight","top","right","bottom","left","padding","paddingTop","paddingRight","paddingBottom","paddingLeft","margin","marginTop","marginRight","marginBottom","marginLeft","backgroundPositionX","backgroundPositionY"]);const mn=e.memo(()=>{try{document.createElement("div").animate({opacity:[1]})}catch(t){return!1}return!0}),pn=new Set(["opacity","clipPath","filter","transform"]);function fn(t,e,n){if(t instanceof EventTarget)return[t];if("string"==typeof t){let s=document;e&&(s=e.current);const i=n?.[t]??s.querySelectorAll(t);return i?Array.from(i):[]}return Array.from(t)}function gn(t){return(e,n)=>{const s=fn(e),i=[];for(const e of s){const s=t(e,n);i.push(s)}return()=>{for(const t of i)t()}}}const yn=(t,e)=>e&&"number"==typeof t?e.transform(t):t;class vn{constructor(){this.latest={},this.values=new Map}set(t,e,n,s,i=!0){const o=this.values.get(t);o&&o.onRemove();const l=()=>{const s=e.get();this.latest[t]=i?yn(s,on[t]):s,n&&r.render(n)};l();const u=e.on("change",l);s&&e.addDependent(s);const c=()=>{u(),n&&a(n),this.values.delete(t),s&&e.removeDependent(s)};return this.values.set(t,{value:e,onRemove:c}),c}get(t){return this.values.get(t)?.value}destroy(){for(const t of this.values.values())t.onRemove()}}function bn(t){const e=new WeakMap,n=[];return(s,i)=>{const r=e.get(s)??new vn;e.set(s,r);for(const e in i){const a=i[e],o=t(s,r,e,a);n.push(o)}return()=>{for(const t of n)t()}}}const Tn=(t,e,n,s)=>{const i=function(t,e){if(!(e in t))return!1;const n=Object.getOwnPropertyDescriptor(Object.getPrototypeOf(t),e)||Object.getOwnPropertyDescriptor(t,e);return n&&"function"==typeof n.set}(t,n),r=i?n:n.startsWith("data")||n.startsWith("aria")?n.replace(/([A-Z])/g,t=>`-${t.toLowerCase()}`):n;const a=i?()=>{t[r]=e.latest[n]}:()=>{const s=e.latest[n];null==s?t.removeAttribute(r):t.setAttribute(r,String(s))};return e.set(n,s,a)},wn=gn(bn(Tn)),Mn=bn((t,e,n,s)=>e.set(n,s,()=>{t[n]=e.latest[n]},void 0,!1));function xn(t){return e.isObject(t)&&"offsetHeight"in t}const Sn={current:void 0};class An{constructor(t,e={}){this.canTrackVelocity=null,this.events={},this.updateAndNotify=t=>{const e=h.now();if(this.updatedAt!==e&&this.setPrevFrameValue(),this.prev=this.current,this.setCurrent(t),this.current!==this.prev&&(this.events.change?.notify(this.current),this.dependents))for(const t of this.dependents)t.dirty()},this.hasAnimated=!1,this.setCurrent(t),this.owner=e.owner}setCurrent(t){var e;this.current=t,this.updatedAt=h.now(),null===this.canTrackVelocity&&void 0!==t&&(this.canTrackVelocity=(e=this.current,!isNaN(parseFloat(e))))}setPrevFrameValue(t=this.current){this.prevFrameValue=t,this.prevUpdatedAt=this.updatedAt}onChange(t){return this.on("change",t)}on(t,n){this.events[t]||(this.events[t]=new e.SubscriptionManager);const s=this.events[t].add(n);return"change"===t?()=>{s(),r.read(()=>{this.events.change.getSize()||this.stop()})}:s}clearListeners(){for(const t in this.events)this.events[t].clear()}attach(t,e){this.passiveEffect=t,this.stopPassiveEffect=e}set(t){this.passiveEffect?this.passiveEffect(t,this.updateAndNotify):this.updateAndNotify(t)}setWithVelocity(t,e,n){this.set(e),this.prev=void 0,this.prevFrameValue=t,this.prevUpdatedAt=this.updatedAt-n}jump(t,e=!0){this.updateAndNotify(t),this.prev=t,this.prevUpdatedAt=this.prevFrameValue=void 0,e&&this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}dirty(){this.events.change?.notify(this.current)}addDependent(t){this.dependents||(this.dependents=new Set),this.dependents.add(t)}removeDependent(t){this.dependents&&this.dependents.delete(t)}get(){return Sn.current&&Sn.current.push(this),this.current}getPrevious(){return this.prev}getVelocity(){const t=h.now();if(!this.canTrackVelocity||void 0===this.prevFrameValue||t-this.updatedAt>30)return 0;const n=Math.min(this.updatedAt-this.prevUpdatedAt,30);return e.velocityPerSecond(parseFloat(this.current)-parseFloat(this.prevFrameValue),n)}start(t){return this.stop(),new Promise(e=>{this.hasAnimated=!0,this.animation=t(e),this.events.animationStart&&this.events.animationStart.notify()}).then(()=>{this.events.animationComplete&&this.events.animationComplete.notify(),this.clearAnimation()})}stop(){this.animation&&(this.animation.stop(),this.events.animationCancel&&this.events.animationCancel.notify()),this.clearAnimation()}isAnimating(){return!!this.animation}clearAnimation(){delete this.animation}destroy(){this.dependents?.clear(),this.events.destroy?.notify(),this.clearListeners(),this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}}function kn(t,e){return new An(t,e)}const En={x:"translateX",y:"translateY",z:"translateZ",transformPerspective:"perspective"};const Vn=new Set(["originX","originY","originZ"]),Pn=(t,e,n,s)=>{let i,r;return le.has(n)?(e.get("transform")||(xn(t)||e.get("transformBox")||Pn(t,e,"transformBox",new An("fill-box")),e.set("transform",new An("none"),()=>{t.style.transform=function(t){let e="",n=!0;for(let s=0;s<oe.length;s++){const i=oe[s],r=t.latest[i];if(void 0===r)continue;let a=!0;a="number"==typeof r?r===(i.startsWith("scale")?1:0):0===parseFloat(r),a||(n=!1,e+=`${En[i]||i}(${t.latest[i]}) `)}return n?"none":e.trim()}(e)})),r=e.get("transform")):Vn.has(n)?(e.get("transformOrigin")||e.set("transformOrigin",new An(""),()=>{const n=e.latest.originX??"50%",s=e.latest.originY??"50%",i=e.latest.originZ??0;t.style.transformOrigin=`${n} ${s} ${i}`}),r=e.get("transformOrigin")):i=we(n)?()=>{t.style.setProperty(n,e.latest[n])}:()=>{t.style[n]=e.latest[n]},e.set(n,s,i,r)},Rn=gn(bn(Pn)),Dn=F.transform;const Fn=gn(bn((t,e,n,s)=>{if(n.startsWith("path"))return function(t,e,n,s){return r.render(()=>t.setAttribute("pathLength","1")),"pathOffset"===n?e.set(n,s,()=>t.setAttribute("stroke-dashoffset",Dn(-e.latest[n]))):(e.get("stroke-dasharray")||e.set("stroke-dasharray",new An("1 1"),()=>{const{pathLength:n=1,pathSpacing:s}=e.latest;t.setAttribute("stroke-dasharray",`${Dn(n)} ${Dn(s??1-Number(n))}`)}),e.set(n,s,void 0,e.get("stroke-dasharray")))}(t,e,n,s);if(n.startsWith("attr"))return Tn(t,e,function(t){return t.replace(/^attr([A-Z])/,(t,e)=>e.toLowerCase())}(n),s);return(n in t.style?Pn:Tn)(t,e,n,s)}));const{schedule:On,cancel:Cn}=i(queueMicrotask,!1),Kn={x:!1,y:!1};function Ln(){return Kn.x||Kn.y}function Wn(t,e){const n=fn(t),s=new AbortController;return[n,{passive:!0,...e,signal:s.signal},()=>s.abort()]}function Bn(t){return!("touch"===t.pointerType||Ln())}const $n=(t,e)=>!!e&&(t===e||$n(t,e.parentElement)),Nn=t=>"mouse"===t.pointerType?"number"!=typeof t.button||t.button<=0:!1!==t.isPrimary,jn=new Set(["BUTTON","INPUT","SELECT","TEXTAREA","A"]);const In=new WeakSet;function Yn(t){return e=>{"Enter"===e.key&&t(e)}}function zn(t,e){t.dispatchEvent(new PointerEvent("pointer"+e,{isPrimary:!0,bubbles:!0}))}function Xn(t){return Nn(t)&&!Ln()}function Gn(t){return e.isObject(t)&&"ownerSVGElement"in t}const Un=new WeakMap;let qn;const Zn=(t,e,n)=>(s,i)=>i&&i[0]?i[0][t+"Size"]:Gn(s)&&"getBBox"in s?s.getBBox()[e]:s[n],_n=Zn("inline","width","offsetWidth"),Hn=Zn("block","height","offsetHeight");function Jn({target:t,borderBoxSize:e}){Un.get(t)?.forEach(n=>{n(t,{get width(){return _n(t,e)},get height(){return Hn(t,e)}})})}function Qn(t){t.forEach(Jn)}function ts(t,e){qn||"undefined"!=typeof ResizeObserver&&(qn=new ResizeObserver(Qn));const n=fn(t);return n.forEach(t=>{let n=Un.get(t);n||(n=new Set,Un.set(t,n)),n.add(e),qn?.observe(t)}),()=>{n.forEach(t=>{const n=Un.get(t);n?.delete(e),n?.size||qn?.unobserve(t)})}}const es=new Set;let ns;function ss(t){return es.add(t),ns||(ns=()=>{const t={get width(){return window.innerWidth},get height(){return window.innerHeight}};es.forEach(e=>e(t))},window.addEventListener("resize",ns)),()=>{es.delete(t),es.size||"function"!=typeof ns||(window.removeEventListener("resize",ns),ns=void 0)}}function is(){const{value:t}=s;null!==t?(t.frameloop.rate.push(o.delta),t.animations.mainThread.push(d.mainThread),t.animations.waapi.push(d.waapi),t.animations.layout.push(d.layout)):a(is)}function rs(t){return t.reduce((t,e)=>t+e,0)/t.length}function as(t,e=rs){return 0===t.length?{min:0,max:0,avg:0}:{min:Math.min(...t),max:Math.max(...t),avg:e(t)}}const os=t=>Math.round(1e3/t);function ls(){s.value=null,s.addProjectionMetrics=null}function us(){const{value:t}=s;if(!t)throw new Error("Stats are not being measured");ls(),a(is);const e={frameloop:{setup:as(t.frameloop.setup),rate:as(t.frameloop.rate),read:as(t.frameloop.read),resolveKeyframes:as(t.frameloop.resolveKeyframes),preUpdate:as(t.frameloop.preUpdate),update:as(t.frameloop.update),preRender:as(t.frameloop.preRender),render:as(t.frameloop.render),postRender:as(t.frameloop.postRender)},animations:{mainThread:as(t.animations.mainThread),waapi:as(t.animations.waapi),layout:as(t.animations.layout)},layoutProjection:{nodes:as(t.layoutProjection.nodes),calculatedTargetDeltas:as(t.layoutProjection.calculatedTargetDeltas),calculatedProjections:as(t.layoutProjection.calculatedProjections)}},{rate:n}=e.frameloop;return n.min=os(n.min),n.max=os(n.max),n.avg=os(n.avg),[n.min,n.max]=[n.max,n.min],e}function cs(t,e){if("first"===t)return 0;{const n=e-1;return"last"===t?n:n/2}}function hs(...t){const e=!Array.isArray(t[0]),n=e?0:-1,s=t[0+n],i=Wt(t[1+n],t[2+n],t[3+n]);return e?i(s):i}function ds(t){const e=[];Sn.current=e;const n=t();Sn.current=void 0;const s=kn(n);return function(t,e,n){const s=()=>e.set(n()),i=()=>r.preRender(s,!1,!0),o=t.map(t=>t.on("change",i));e.on("destroy",()=>{o.forEach(t=>t()),a(s)})}(e,s,t),s}const ms=t=>Boolean(t&&t.getVelocity);function ps(t,e,n){const s=t.get();let i,a=null,o=s;const l="string"==typeof s?s.replace(/[\d.-]/g,""):void 0,u=()=>{a&&(a.stop(),a=null)},c=()=>{u(),a=new Zt({keyframes:[gs(t.get()),gs(o)],velocity:t.getVelocity(),type:"spring",restDelta:.001,restSpeed:.01,...n,onUpdate:i})};if(t.attach((e,n)=>(o=e,i=t=>n(fs(t,l)),r.postRender(c),t.get()),u),ms(e)){const n=e.on("change",e=>t.set(fs(e,l))),s=t.on("destroy",n);return()=>{n(),s()}}return u}function fs(t,e){return e?t+e:t}function gs(t){return"number"==typeof t?t:parseFloat(t)}const ys=[...He,W,G];function vs(t){return"layout"===t?"group":"enter"===t||"new"===t?"new":"exit"===t||"old"===t?"old":"group"}let bs={},Ts=null;const ws=(t,e)=>{bs[t]=e},Ms=()=>{Ts||(Ts=document.createElement("style"),Ts.id="motion-view");let t="";for(const e in bs){const n=bs[e];t+=`${e} {\n`;for(const[e,s]of Object.entries(n))t+=`  ${e}: ${s};\n`;t+="}\n"}Ts.textContent=t,document.head.appendChild(Ts),bs={}},xs=()=>{Ts&&Ts.parentElement&&Ts.parentElement.removeChild(Ts)};function Ss(t){const e=t.match(/::view-transition-(old|new|group|image-pair)\((.*?)\)/);return e?{layer:e[2],type:e[1]}:null}function As(t){const{effect:e}=t;return!!e&&(e.target===document.documentElement&&e.pseudoElement?.startsWith("::view-transition"))}function ks(){return document.getAnimations().filter(As)}const Es=["layout","enter","exit","new","old"];function Vs(t){const{update:n,targets:s,options:i}=t;if(!document.startViewTransition)return new Promise(async t=>{await n(),t(new Ie([]))});(function(t,e){return e.has(t)&&Object.keys(e.get(t)).length>0})("root",s)||ws(":root",{"view-transition-name":"none"}),ws("::view-transition-group(*), ::view-transition-old(*), ::view-transition-new(*)",{"animation-timing-function":"linear !important"}),Ms();const r=document.startViewTransition(async()=>{await n()});return r.finished.finally(()=>{xs()}),new Promise(t=>{r.ready.then(()=>{const n=ks(),r=[];s.forEach((t,n)=>{for(const s of Es){if(!t[s])continue;const{keyframes:a,options:o}=t[s];for(let[t,l]of Object.entries(a)){if(!l)continue;const a={...qe(i,t),...qe(o,t)},u=vs(s);if("opacity"===t&&!Array.isArray(l)){l=["new"===u?0:1,l]}"function"==typeof a.delay&&(a.delay=a.delay(0,1)),a.duration&&(a.duration=e.secondsToMilliseconds(a.duration)),a.delay&&(a.delay=e.secondsToMilliseconds(a.delay));const c=new Oe({...a,element:document.documentElement,name:t,pseudoElement:`::view-transition-${u}(${n})`,keyframes:l});r.push(c)}}});for(const t of n){if("finished"===t.playState)continue;const{effect:n}=t;if(!(n&&n instanceof KeyframeEffect))continue;const{pseudoElement:a}=n;if(!a)continue;const o=Ss(a);if(!o)continue;const l=s.get(o.layer);if(l)Ps(l,"enter")&&Ps(l,"exit")&&n.getKeyframes().some(t=>t.mixBlendMode)?r.push(new Ye(t)):t.cancel();else{const s="group"===o.type?"layout":"";let a={...qe(i,s)};a.duration&&(a.duration=e.secondsToMilliseconds(a.duration)),a=Fe(a);const l=Pe(a.ease,a.duration);n.updateTiming({delay:e.secondsToMilliseconds(a.delay??0),duration:a.duration,easing:l}),r.push(new Ye(t))}}t(new Ie(r))})})}function Ps(t,e){return t?.[e]?.keyframes.opacity}let Rs=[],Ds=null;function Fs(){Ds=null;const[t]=Rs;var n;t&&(n=t,e.removeItem(Rs,n),Ds=n,Vs(n).then(t=>{n.notifyReady(t),t.finished.finally(Fs)}))}function Os(){for(let t=Rs.length-1;t>=0;t--){const e=Rs[t],{interrupt:n}=e.options;if("immediate"===n){const n=Rs.slice(0,t+1).map(t=>t.update),s=Rs.slice(t+1);e.update=()=>{n.forEach(t=>t())},Rs=[e,...s];break}}Ds&&"immediate"!==Rs[0]?.options.interrupt||Fs()}class Cs{constructor(t,n={}){var s;this.currentSubject="root",this.targets=new Map,this.notifyReady=e.noop,this.readyPromise=new Promise(t=>{this.notifyReady=t}),this.update=t,this.options={interrupt:"wait",...n},s=this,Rs.push(s),On.render(Os)}get(t){return this.currentSubject=t,this}layout(t,e){return this.updateTarget("layout",t,e),this}new(t,e){return this.updateTarget("new",t,e),this}old(t,e){return this.updateTarget("old",t,e),this}enter(t,e){return this.updateTarget("enter",t,e),this}exit(t,e){return this.updateTarget("exit",t,e),this}crossfade(t){return this.updateTarget("enter",{opacity:1},t),this.updateTarget("exit",{opacity:0},t),this}updateTarget(t,e,n={}){const{currentSubject:s,targets:i}=this;i.has(s)||i.set(s,{});i.get(s)[t]={keyframes:e,options:n}}then(t,e){return this.readyPromise.then(t,e)}}const Ks=r,Ls=n.reduce((t,e)=>(t[e]=t=>a(t),t),{});t.AsyncMotionValueAnimation=class extends Ut{constructor({autoplay:t=!0,delay:e=0,type:n="keyframes",repeat:s=0,repeatDelay:i=0,repeatType:r="loop",keyframes:a,name:o,motionValue:l,element:u,...c}){super(),this.stop=()=>{this._animation&&(this._animation.stop(),this.stopTimeline?.()),this.keyframeResolver?.cancel()},this.createdAt=h.now();const d={autoplay:t,delay:e,type:n,repeat:s,repeatDelay:i,repeatType:r,name:o,motionValue:l,element:u,...c},m=u?.KeyframeResolver||Te;this.keyframeResolver=new m(a,(t,e,n)=>this.onKeyframesResolved(t,e,d,!n),o,l,u),this.keyframeResolver?.scheduleResolve()}onKeyframesResolved(t,n,s,i){this.keyframeResolver=void 0;const{name:r,type:a,velocity:o,delay:l,isHandoff:u,onUpdate:c}=s;this.resolvedAt=h.now(),function(t,n,s,i){const r=t[0];if(null===r)return!1;if("display"===n||"visibility"===n)return!0;const a=t[t.length-1],o=We(r,n),l=We(a,n);return e.warning(o===l,`You are trying to animate ${n} from "${r}" to "${a}". "${o?a:r}" is not an animatable value.`,"value-not-animatable"),!(!o||!l)&&(function(t){const e=t[0];if(1===t.length)return!0;for(let n=0;n<t.length;n++)if(t[n]!==e)return!0}(t)||("spring"===s||De(s))&&i)}(t,r,a,o)||(!e.MotionGlobalConfig.instantAnimations&&l||c?.(zt(t,s,n)),t[0]=t[t.length-1],Be(s),s.repeat=0);const d={startTime:i?this.resolvedAt&&this.resolvedAt-this.createdAt>40?this.resolvedAt:this.createdAt:void 0,finalKeyframe:n,...s,keyframes:t},m=!u&&je(d)?new Le({...d,element:d.motionValue.owner.current}):new Zt(d);m.finished.then(()=>this.notifyFinished()).catch(e.noop),this.pendingTimeline&&(this.stopTimeline=m.attachTimeline(this.pendingTimeline),this.pendingTimeline=void 0),this._animation=m}get finished(){return this._animation?this.animation.finished:this._finished}then(t,e){return this.finished.finally(t).then(()=>{})}get animation(){return this._animation||(this.keyframeResolver?.resume(),be()),this._animation}get duration(){return this.animation.duration}get time(){return this.animation.time}set time(t){this.animation.time=t}get speed(){return this.animation.speed}get state(){return this.animation.state}set speed(t){this.animation.speed=t}get startTime(){return this.animation.startTime}attachTimeline(t){return this._animation?this.stopTimeline=this.animation.attachTimeline(t):this.pendingTimeline=t,()=>this.stop()}play(){this.animation.play()}pause(){this.animation.pause()}complete(){this.animation.complete()}cancel(){this._animation&&this.animation.cancel(),this.keyframeResolver?.cancel()}},t.DOMKeyframesResolver=class extends Te{constructor(t,e,n,s,i){super(t,e,n,s,i,!0)}readKeyframes(){const{unresolvedKeyframes:t,element:e,name:n}=this;if(!e||!e.current)return;super.readKeyframes();for(let n=0;n<t.length;n++){let s=t[n];if("string"==typeof s&&(s=s.trim(),g(s))){const i=Ue(s,e.current);void 0!==i&&(t[n]=i),n===t.length-1&&(this.finalKeyframe=s)}}if(this.resolveNoneKeyframes(),!Ze.has(n)||2!==t.length)return;const[s,i]=t,r=Je(s),a=Je(i);if(r!==a)if(ue(r)&&ue(a))for(let e=0;e<t.length;e++){const n=t[e];"string"==typeof n&&(t[e]=parseFloat(n))}else de[n]&&(this.needsMeasurement=!0)}resolveNoneKeyframes(){const{unresolvedKeyframes:t,name:e}=this,n=[];for(let e=0;e<t.length;e++)(null===t[e]||Qe(t[e]))&&n.push(e);n.length&&function(t,e,n){let s,i=0;for(;i<t.length&&!s;){const e=t[i];"string"==typeof e&&!hn.has(e)&&I(e).values.length&&(s=t[i]),i++}if(s&&n)for(const i of e)t[i]=cn(n,s)}(t,n,e)}measureInitialState(){const{element:t,unresolvedKeyframes:e,name:n}=this;if(!t||!t.current)return;"height"===n&&(this.suspendedScrollY=window.pageYOffset),this.measuredOrigin=de[n](t.measureViewportBox(),window.getComputedStyle(t.current)),e[0]=this.measuredOrigin;const s=e[e.length-1];void 0!==s&&t.getValue(n,s).jump(s,!1)}measureEndState(){const{element:t,name:e,unresolvedKeyframes:n}=this;if(!t||!t.current)return;const s=t.getValue(e);s&&s.jump(this.measuredOrigin,!1);const i=n.length-1,r=n[i];n[i]=de[e](t.measureViewportBox(),window.getComputedStyle(t.current)),null!==r&&void 0===this.finalKeyframe&&(this.finalKeyframe=r),this.removedTransforms?.length&&this.removedTransforms.forEach(([e,n])=>{t.getValue(e).set(n)}),this.resolveNoneKeyframes()}},t.GroupAnimation=Ie,t.GroupAnimationWithThen=class extends Ie{then(t,e){return this.finished.finally(t).then(()=>{})}},t.JSAnimation=Zt,t.KeyframeResolver=Te,t.MotionValue=An,t.NativeAnimation=Oe,t.NativeAnimationExtended=Le,t.NativeAnimationWrapper=Ye,t.ViewTransitionBuilder=Cs,t.acceleratedValues=pn,t.activeAnimations=d,t.addAttrValue=Tn,t.addStyleValue=Pn,t.alpha=b,t.analyseComplexValue=I,t.animateValue=function(t){return new Zt(t)},t.animateView=function(t,e={}){return new Cs(t,e)},t.animationMapKey=(t,e="")=>`${t}:${e}`,t.applyGeneratorOptions=Fe,t.applyPxDefaults=function(t,e){for(let n=0;n<t.length;n++)"number"==typeof t[n]&&dn.has(e)&&(t[n]=t[n]+"px")},t.attachSpring=ps,t.attrEffect=wn,t.calcGeneratorDuration=dt,t.cancelFrame=a,t.cancelMicrotask=Cn,t.cancelSync=Ls,t.collectMotionValues=Sn,t.color=W,t.complex=G,t.convertOffsetToTimes=Nt,t.createGeneratorEasing=mt,t.createRenderBatcher=i,t.cubicBezierAsString=Ee,t.defaultEasing=jt,t.defaultOffset=$t,t.defaultTransformValue=ie,t.defaultValueTypes=ln,t.degrees=R,t.dimensionValueTypes=He,t.fillOffset=Bt,t.fillWildcards=_t,t.findDimensionValueType=Je,t.findValueType=t=>ys.find(_e(t)),t.flushKeyframeResolvers=be,t.frame=r,t.frameData=o,t.frameSteps=l,t.generateLinearEasing=ct,t.getAnimatableNone=cn,t.getAnimationMap=function(t){const e=ze.get(t)||new Map;return ze.set(t,e),e},t.getComputedStyle=function(t,e){const n=window.getComputedStyle(t);return we(e)?n.getPropertyValue(e):n[e]},t.getDefaultValueType=un,t.getMixer=it,t.getOriginIndex=cs,t.getValueAsType=yn,t.getValueTransition=qe,t.getVariableValue=Ue,t.getViewAnimationLayerInfo=Ss,t.getViewAnimations=ks,t.hex=V,t.hover=function(t,e,n={}){const[s,i,r]=Wn(t,n),a=t=>{if(!Bn(t))return;const{target:n}=t,s=e(n,t);if("function"!=typeof s||!n)return;const r=t=>{Bn(t)&&(s(t),n.removeEventListener("pointerleave",r))};n.addEventListener("pointerleave",r,i)};return s.forEach(t=>{t.addEventListener("pointerenter",a,i)}),r},t.hsla=L,t.hslaToRgba=q,t.inertia=Lt,t.interpolate=Wt,t.invisibleValues=et,t.isCSSVariableName=p,t.isCSSVariableToken=g,t.isDragActive=Ln,t.isDragging=Kn,t.isGenerator=De,t.isHTMLElement=xn,t.isMotionValue=ms,t.isNodeOrChild=$n,t.isPrimaryPointer=Nn,t.isSVGElement=Gn,t.isSVGSVGElement=function(t){return Gn(t)&&"svg"===t.tagName},t.isWaapiSupportedEasing=function t(n){return Boolean("function"==typeof n&&ke()||!n||"string"==typeof n&&(n in Ve||ke())||e.isBezierDefinition(n)||Array.isArray(n)&&n.every(t))},t.keyframes=It,t.makeAnimationInstant=Be,t.mapEasingToNativeEasing=Pe,t.mapValue=function(t,e,n,s){const i=hs(e,n,s);return ds(()=>i(t.get()))},t.maxGeneratorDuration=ht,t.microtask=On,t.mix=lt,t.mixArray=rt,t.mixColor=tt,t.mixComplex=ot,t.mixImmediate=Z,t.mixLinearColor=H,t.mixNumber=_,t.mixObject=at,t.mixVisibility=nt,t.motionValue=kn,t.number=v,t.numberValueTypes=on,t.observeTimeline=function(t,e){let n;const s=()=>{const{currentTime:s}=e,i=(null===s?0:s.value)/100;n!==i&&t(i),n=i};return r.preUpdate(s,!0),()=>a(s)},t.parseCSSVariable=Ge,t.parseValueFromTransform=re,t.percent=D,t.positionalKeys=Ze,t.press=function(t,e,n={}){const[s,i,r]=Wn(t,n),a=t=>{const s=t.currentTarget;if(!Xn(t))return;In.add(s);const r=e(s,t),a=(t,e)=>{window.removeEventListener("pointerup",o),window.removeEventListener("pointercancel",l),In.has(s)&&In.delete(s),Xn(t)&&"function"==typeof r&&r(t,{success:e})},o=t=>{a(t,s===window||s===document||n.useGlobalTarget||$n(s,t.target))},l=t=>{a(t,!1)};window.addEventListener("pointerup",o,i),window.addEventListener("pointercancel",l,i)};return s.forEach(t=>{var e;(n.useGlobalTarget?window:t).addEventListener("pointerdown",a,i),xn(t)&&(t.addEventListener("focus",t=>((t,e)=>{const n=t.currentTarget;if(!n)return;const s=Yn(()=>{if(In.has(n))return;zn(n,"down");const t=Yn(()=>{zn(n,"up")});n.addEventListener("keyup",t,e),n.addEventListener("blur",()=>zn(n,"cancel"),e)});n.addEventListener("keydown",s,e),n.addEventListener("blur",()=>n.removeEventListener("keydown",s),e)})(t,i)),e=t,jn.has(e.tagName)||-1!==e.tabIndex||t.hasAttribute("tabindex")||(t.tabIndex=0))}),r},t.progressPercentage=K,t.propEffect=Mn,t.px=F,t.readTransformValue=(t,e)=>{const{transform:n="none"}=getComputedStyle(t);return re(n,e)},t.recordStats=function(){if(s.value)throw ls(),new Error("Stats are already being measured");const t=s;return t.value={frameloop:{setup:[],rate:[],read:[],resolveKeyframes:[],preUpdate:[],update:[],preRender:[],render:[],postRender:[]},animations:{mainThread:[],waapi:[],layout:[]},layoutProjection:{nodes:[],calculatedTargetDeltas:[],calculatedProjections:[]}},t.addProjectionMetrics=e=>{const{layoutProjection:n}=t.value;n.nodes.push(e.nodes),n.calculatedTargetDeltas.push(e.calculatedTargetDeltas),n.calculatedProjections.push(e.calculatedProjections)},r.postRender(is,!0),us},t.resize=function(t,e){return"function"==typeof t?ss(t):ts(t,e)},t.resolveElements=fn,t.rgbUnit=k,t.rgba=E,t.scale=T,t.setDragLock=function(t){return"x"===t||"y"===t?Kn[t]?null:(Kn[t]=!0,()=>{Kn[t]=!1}):Kn.x||Kn.y?null:(Kn.x=Kn.y=!0,()=>{Kn.x=Kn.y=!1})},t.setStyle=Me,t.spring=Kt,t.springValue=function(t,e){const n=kn(ms(t)?t.get():t);return ps(n,t,e),n},t.stagger=function(t=.1,{startDelay:n=0,from:s=0,ease:i}={}){return(r,a)=>{const o="number"==typeof s?s:cs(s,a),l=Math.abs(o-r);let u=t*l;if(i){const n=a*t;u=e.easingDefinitionToFunction(i)(u/n)*n}return n+u}},t.startWaapiAnimation=Re,t.statsBuffer=s,t.styleEffect=Rn,t.supportedWaapiEasing=Ve,t.supportsBrowserAnimation=je,t.supportsFlags=Se,t.supportsLinearEasing=ke,t.supportsPartialKeyframes=mn,t.supportsScrollTimeline=xe,t.svgEffect=Fn,t.sync=Ks,t.testValueType=_e,t.time=h,t.transform=hs,t.transformPropOrder=oe,t.transformProps=le,t.transformValue=ds,t.transformValueTypes=an,t.vh=O,t.vw=C});
