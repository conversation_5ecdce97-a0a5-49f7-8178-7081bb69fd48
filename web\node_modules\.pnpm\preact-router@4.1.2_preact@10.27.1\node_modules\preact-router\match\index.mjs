import{h as a}from"preact";import{useRouter as t,exec as r,<PERSON> as e}from"preact-router";var s=["className","activeClass","activeClassName","path"];function l(a){var e=t()[0];return a.children({url:e.url,path:e.path,matches:!1!==r(e.path||e.url,a.path,{})})}function c(l){var c=l.className,n=l.activeClass,u=l.activeClassName,i=l.path,p=function(a,t){if(null==a)return{};var r,e,s={},l=Object.keys(a);for(e=0;e<l.length;e++)t.indexOf(r=l[e])>=0||(s[r]=a[r]);return s}(l,s),h=t()[0],f=i&&h.path&&r(h.path,i,{})||r(h.url,p.href,{}),o=p.class||c||"",m=f&&(n||u)||"";return p.class=o+(o&&m&&" ")+m,a(e,p)}export{c as Link,l as Match,l as default};
