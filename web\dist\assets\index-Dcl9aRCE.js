(function(){const t=document.createElement("link").relList;if(t&&t.supports&&t.supports("modulepreload"))return;for(const r of document.querySelectorAll('link[rel="modulepreload"]'))i(r);new MutationObserver(r=>{for(const o of r)if(o.type==="childList")for(const c of o.addedNodes)c.tagName==="LINK"&&c.rel==="modulepreload"&&i(c)}).observe(document,{childList:!0,subtree:!0});function n(r){const o={};return r.integrity&&(o.integrity=r.integrity),r.referrerPolicy&&(o.referrerPolicy=r.referrerPolicy),r.crossOrigin==="use-credentials"?o.credentials="include":r.crossOrigin==="anonymous"?o.credentials="omit":o.credentials="same-origin",o}function i(r){if(r.ep)return;r.ep=!0;const o=n(r);fetch(r.href,o)}})();var W,b,Ue,U,ve,Ie,Oe,Ee,ce,te,ne,Me,D={},Ne=[],Xe=/acit|ex(?:s|g|n|p|$)|rph|grid|ows|mnc|ntw|ine[ch]|zoo|^ord|itera/i,G=Array.isArray;function w(e,t){for(var n in t)e[n]=t[n];return e}function ae(e){e&&e.parentNode&&e.parentNode.removeChild(e)}function _e(e,t,n){var i,r,o,c={};for(o in t)o=="key"?i=t[o]:o=="ref"?r=t[o]:c[o]=t[o];if(arguments.length>2&&(c.children=arguments.length>3?W.call(arguments,2):n),typeof e=="function"&&e.defaultProps!=null)for(o in e.defaultProps)c[o]===void 0&&(c[o]=e.defaultProps[o]);return R(e,c,i,r,null)}function R(e,t,n,i,r){var o={type:e,props:t,key:n,ref:i,__k:null,__:null,__b:0,__e:null,__c:null,constructor:void 0,__v:r??++Ue,__i:-1,__u:0};return r==null&&b.vnode!=null&&b.vnode(o),o}function X(e){return e.children}function H(e,t){this.props=e,this.context=t}function E(e,t){if(t==null)return e.__?E(e.__,e.__i+1):null;for(var n;t<e.__k.length;t++)if((n=e.__k[t])!=null&&n.__e!=null)return n.__e;return typeof e.type=="function"?E(e):null}function Re(e){var t,n;if((e=e.__)!=null&&e.__c!=null){for(e.__e=e.__c.base=null,t=0;t<e.__k.length;t++)if((n=e.__k[t])!=null&&n.__e!=null){e.__e=e.__c.base=n.__e;break}return Re(e)}}function ie(e){(!e.__d&&(e.__d=!0)&&U.push(e)&&!V.__r++||ve!=b.debounceRendering)&&((ve=b.debounceRendering)||Ie)(V)}function V(){for(var e,t,n,i,r,o,c,a=1;U.length;)U.length>a&&U.sort(Oe),e=U.shift(),a=U.length,e.__d&&(n=void 0,r=(i=(t=e).__v).__e,o=[],c=[],t.__P&&((n=w({},i)).__v=i.__v+1,b.vnode&&b.vnode(n),ue(t.__P,n,i,t.__n,t.__P.namespaceURI,32&i.__u?[r]:null,o,r??E(i),!!(32&i.__u),c),n.__v=i.__v,n.__.__k[n.__i]=n,Fe(o,n,c),n.__e!=r&&Re(n)));V.__r=0}function He(e,t,n,i,r,o,c,a,p,_,v){var s,u,h,m,d,f,g,y=i&&i.__k||Ne,A=t.length;for(p=Ye(n,t,y,p,A),s=0;s<A;s++)(h=n.__k[s])!=null&&(u=h.__i==-1?D:y[h.__i]||D,h.__i=s,f=ue(e,h,u,r,o,c,a,p,_,v),m=h.__e,h.ref&&u.ref!=h.ref&&(u.ref&&de(u.ref,null,h),v.push(h.ref,h.__c||m,h)),d==null&&m!=null&&(d=m),(g=!!(4&h.__u))||u.__k===h.__k?p=De(h,p,e,g):typeof h.type=="function"&&f!==void 0?p=f:m&&(p=m.nextSibling),h.__u&=-7);return n.__e=d,p}function Ye(e,t,n,i,r){var o,c,a,p,_,v=n.length,s=v,u=0;for(e.__k=new Array(r),o=0;o<r;o++)(c=t[o])!=null&&typeof c!="boolean"&&typeof c!="function"?(p=o+u,(c=e.__k[o]=typeof c=="string"||typeof c=="number"||typeof c=="bigint"||c.constructor==String?R(null,c,null,null,null):G(c)?R(X,{children:c},null,null,null):c.constructor==null&&c.__b>0?R(c.type,c.props,c.key,c.ref?c.ref:null,c.__v):c).__=e,c.__b=e.__b+1,a=null,(_=c.__i=Je(c,n,p,s))!=-1&&(s--,(a=n[_])&&(a.__u|=2)),a==null||a.__v==null?(_==-1&&(r>v?u--:r<v&&u++),typeof c.type!="function"&&(c.__u|=4)):_!=p&&(_==p-1?u--:_==p+1?u++:(_>p?u--:u++,c.__u|=4))):e.__k[o]=null;if(s)for(o=0;o<v;o++)(a=n[o])!=null&&(2&a.__u)==0&&(a.__e==i&&(i=E(a)),We(a,a));return i}function De(e,t,n,i){var r,o;if(typeof e.type=="function"){for(r=e.__k,o=0;r&&o<r.length;o++)r[o]&&(r[o].__=e,t=De(r[o],t,n,i));return t}e.__e!=t&&(i&&(t&&e.type&&!t.parentNode&&(t=E(e)),n.insertBefore(e.__e,t||null)),t=e.__e);do t=t&&t.nextSibling;while(t!=null&&t.nodeType==8);return t}function re(e,t){return t=t||[],e==null||typeof e=="boolean"||(G(e)?e.some(function(n){re(n,t)}):t.push(e)),t}function Je(e,t,n,i){var r,o,c,a=e.key,p=e.type,_=t[n],v=_!=null&&(2&_.__u)==0;if(_===null&&e.key==null||v&&a==_.key&&p==_.type)return n;if(i>(v?1:0)){for(r=n-1,o=n+1;r>=0||o<t.length;)if((_=t[c=r>=0?r--:o++])!=null&&(2&_.__u)==0&&a==_.key&&p==_.type)return c}return-1}function me(e,t,n){t[0]=="-"?e.setProperty(t,n??""):e[t]=n==null?"":typeof n!="number"||Xe.test(t)?n:n+"px"}function B(e,t,n,i,r){var o,c;e:if(t=="style")if(typeof n=="string")e.style.cssText=n;else{if(typeof i=="string"&&(e.style.cssText=i=""),i)for(t in i)n&&t in n||me(e.style,t,"");if(n)for(t in n)i&&n[t]==i[t]||me(e.style,t,n[t])}else if(t[0]=="o"&&t[1]=="n")o=t!=(t=t.replace(Ee,"$1")),c=t.toLowerCase(),t=c in e||t=="onFocusOut"||t=="onFocusIn"?c.slice(2):t.slice(2),e.l||(e.l={}),e.l[t+o]=n,n?i?n.u=i.u:(n.u=ce,e.addEventListener(t,o?ne:te,o)):e.removeEventListener(t,o?ne:te,o);else{if(r=="http://www.w3.org/2000/svg")t=t.replace(/xlink(H|:h)/,"h").replace(/sName$/,"s");else if(t!="width"&&t!="height"&&t!="href"&&t!="list"&&t!="form"&&t!="tabIndex"&&t!="download"&&t!="rowSpan"&&t!="colSpan"&&t!="role"&&t!="popover"&&t in e)try{e[t]=n??"";break e}catch{}typeof n=="function"||(n==null||n===!1&&t[4]!="-"?e.removeAttribute(t):e.setAttribute(t,t=="popover"&&n==1?"":n))}}function ye(e){return function(t){if(this.l){var n=this.l[t.type+e];if(t.t==null)t.t=ce++;else if(t.t<n.u)return;return n(b.event?b.event(t):t)}}}function ue(e,t,n,i,r,o,c,a,p,_){var v,s,u,h,m,d,f,g,y,A,x,q,M,ge,j,N,J,P=t.type;if(t.constructor!=null)return null;128&n.__u&&(p=!!(32&n.__u),o=[a=t.__e=n.__e]),(v=b.__b)&&v(t);e:if(typeof P=="function")try{if(g=t.props,y="prototype"in P&&P.prototype.render,A=(v=P.contextType)&&i[v.__c],x=v?A?A.props.value:v.__:i,n.__c?f=(s=t.__c=n.__c).__=s.__E:(y?t.__c=s=new P(g,x):(t.__c=s=new H(g,x),s.constructor=P,s.render=Ze),A&&A.sub(s),s.props=g,s.state||(s.state={}),s.context=x,s.__n=i,u=s.__d=!0,s.__h=[],s._sb=[]),y&&s.__s==null&&(s.__s=s.state),y&&P.getDerivedStateFromProps!=null&&(s.__s==s.state&&(s.__s=w({},s.__s)),w(s.__s,P.getDerivedStateFromProps(g,s.__s))),h=s.props,m=s.state,s.__v=t,u)y&&P.getDerivedStateFromProps==null&&s.componentWillMount!=null&&s.componentWillMount(),y&&s.componentDidMount!=null&&s.__h.push(s.componentDidMount);else{if(y&&P.getDerivedStateFromProps==null&&g!==h&&s.componentWillReceiveProps!=null&&s.componentWillReceiveProps(g,x),!s.__e&&s.shouldComponentUpdate!=null&&s.shouldComponentUpdate(g,s.__s,x)===!1||t.__v==n.__v){for(t.__v!=n.__v&&(s.props=g,s.state=s.__s,s.__d=!1),t.__e=n.__e,t.__k=n.__k,t.__k.some(function(O){O&&(O.__=t)}),q=0;q<s._sb.length;q++)s.__h.push(s._sb[q]);s._sb=[],s.__h.length&&c.push(s);break e}s.componentWillUpdate!=null&&s.componentWillUpdate(g,s.__s,x),y&&s.componentDidUpdate!=null&&s.__h.push(function(){s.componentDidUpdate(h,m,d)})}if(s.context=x,s.props=g,s.__P=e,s.__e=!1,M=b.__r,ge=0,y){for(s.state=s.__s,s.__d=!1,M&&M(t),v=s.render(s.props,s.state,s.context),j=0;j<s._sb.length;j++)s.__h.push(s._sb[j]);s._sb=[]}else do s.__d=!1,M&&M(t),v=s.render(s.props,s.state,s.context),s.state=s.__s;while(s.__d&&++ge<25);s.state=s.__s,s.getChildContext!=null&&(i=w(w({},i),s.getChildContext())),y&&!u&&s.getSnapshotBeforeUpdate!=null&&(d=s.getSnapshotBeforeUpdate(h,m)),N=v,v!=null&&v.type===X&&v.key==null&&(N=Le(v.props.children)),a=He(e,G(N)?N:[N],t,n,i,r,o,c,a,p,_),s.base=t.__e,t.__u&=-161,s.__h.length&&c.push(s),f&&(s.__E=s.__=null)}catch(O){if(t.__v=null,p||o!=null)if(O.then){for(t.__u|=p?160:128;a&&a.nodeType==8&&a.nextSibling;)a=a.nextSibling;o[o.indexOf(a)]=null,t.__e=a}else{for(J=o.length;J--;)ae(o[J]);oe(t)}else t.__e=n.__e,t.__k=n.__k,O.then||oe(t);b.__e(O,t,n)}else o==null&&t.__v==n.__v?(t.__k=n.__k,t.__e=n.__e):a=t.__e=Qe(n.__e,t,n,i,r,o,c,p,_);return(v=b.diffed)&&v(t),128&t.__u?void 0:a}function oe(e){e&&e.__c&&(e.__c.__e=!0),e&&e.__k&&e.__k.forEach(oe)}function Fe(e,t,n){for(var i=0;i<n.length;i++)de(n[i],n[++i],n[++i]);b.__c&&b.__c(t,e),e.some(function(r){try{e=r.__h,r.__h=[],e.some(function(o){o.call(r)})}catch(o){b.__e(o,r.__v)}})}function Le(e){return typeof e!="object"||e==null||e.__b&&e.__b>0?e:G(e)?e.map(Le):w({},e)}function Qe(e,t,n,i,r,o,c,a,p){var _,v,s,u,h,m,d,f=n.props,g=t.props,y=t.type;if(y=="svg"?r="http://www.w3.org/2000/svg":y=="math"?r="http://www.w3.org/1998/Math/MathML":r||(r="http://www.w3.org/1999/xhtml"),o!=null){for(_=0;_<o.length;_++)if((h=o[_])&&"setAttribute"in h==!!y&&(y?h.localName==y:h.nodeType==3)){e=h,o[_]=null;break}}if(e==null){if(y==null)return document.createTextNode(g);e=document.createElementNS(r,y,g.is&&g),a&&(b.__m&&b.__m(t,o),a=!1),o=null}if(y==null)f===g||a&&e.data==g||(e.data=g);else{if(o=o&&W.call(e.childNodes),f=n.props||D,!a&&o!=null)for(f={},_=0;_<e.attributes.length;_++)f[(h=e.attributes[_]).name]=h.value;for(_ in f)if(h=f[_],_!="children"){if(_=="dangerouslySetInnerHTML")s=h;else if(!(_ in g)){if(_=="value"&&"defaultValue"in g||_=="checked"&&"defaultChecked"in g)continue;B(e,_,null,h,r)}}for(_ in g)h=g[_],_=="children"?u=h:_=="dangerouslySetInnerHTML"?v=h:_=="value"?m=h:_=="checked"?d=h:a&&typeof h!="function"||f[_]===h||B(e,_,h,f[_],r);if(v)a||s&&(v.__html==s.__html||v.__html==e.innerHTML)||(e.innerHTML=v.__html),t.__k=[];else if(s&&(e.innerHTML=""),He(t.type=="template"?e.content:e,G(u)?u:[u],t,n,i,y=="foreignObject"?"http://www.w3.org/1999/xhtml":r,o,c,o?o[0]:n.__k&&E(n,0),a,p),o!=null)for(_=o.length;_--;)ae(o[_]);a||(_="value",y=="progress"&&m==null?e.removeAttribute("value"):m!=null&&(m!==e[_]||y=="progress"&&!m||y=="option"&&m!=f[_])&&B(e,_,m,f[_],r),_="checked",d!=null&&d!=e[_]&&B(e,_,d,f[_],r))}return e}function de(e,t,n){try{if(typeof e=="function"){var i=typeof e.__u=="function";i&&e.__u(),i&&t==null||(e.__u=e(t))}else e.current=t}catch(r){b.__e(r,n)}}function We(e,t,n){var i,r;if(b.unmount&&b.unmount(e),(i=e.ref)&&(i.current&&i.current!=e.__e||de(i,null,t)),(i=e.__c)!=null){if(i.componentWillUnmount)try{i.componentWillUnmount()}catch(o){b.__e(o,t)}i.base=i.__P=null}if(i=e.__k)for(r=0;r<i.length;r++)i[r]&&We(i[r],t,n||typeof e.type!="function");n||ae(e.__e),e.__c=e.__=e.__e=void 0}function Ze(e,t,n){return this.constructor(e,n)}function et(e,t,n){var i,r,o,c;t==document&&(t=document.documentElement),b.__&&b.__(e,t),r=(i=!1)?null:t.__k,o=[],c=[],ue(t,e=t.__k=_e(X,null,[e]),r||D,D,t.namespaceURI,r?null:t.firstChild?W.call(t.childNodes):null,o,r?r.__e:t.firstChild,i,c),Fe(o,e,c)}function tt(e,t,n){var i,r,o,c,a=w({},e.props);for(o in e.type&&e.type.defaultProps&&(c=e.type.defaultProps),t)o=="key"?i=t[o]:o=="ref"?r=t[o]:a[o]=t[o]===void 0&&c!=null?c[o]:t[o];return arguments.length>2&&(a.children=arguments.length>3?W.call(arguments,2):n),R(e.type,a,i||e.key,r||e.ref,null)}function nt(e){function t(n){var i,r;return this.getChildContext||(i=new Set,(r={})[t.__c]=this,this.getChildContext=function(){return r},this.componentWillUnmount=function(){i=null},this.shouldComponentUpdate=function(o){this.props.value!=o.value&&i.forEach(function(c){c.__e=!0,ie(c)})},this.sub=function(o){i.add(o);var c=o.componentWillUnmount;o.componentWillUnmount=function(){i&&i.delete(o),c&&c.call(o)}}),n.children}return t.__c="__cC"+Me++,t.__=e,t.Provider=t.__l=(t.Consumer=function(n,i){return n.children(i)}).contextType=t,t}W=Ne.slice,b={__e:function(e,t,n,i){for(var r,o,c;t=t.__;)if((r=t.__c)&&!r.__)try{if((o=r.constructor)&&o.getDerivedStateFromError!=null&&(r.setState(o.getDerivedStateFromError(e)),c=r.__d),r.componentDidCatch!=null&&(r.componentDidCatch(e,i||{}),c=r.__d),c)return r.__E=r}catch(a){e=a}throw e}},Ue=0,H.prototype.setState=function(e,t){var n;n=this.__s!=null&&this.__s!=this.state?this.__s:this.__s=w({},this.state),typeof e=="function"&&(e=e(w({},n),this.props)),e&&w(n,e),e!=null&&this.__v&&(t&&this._sb.push(t),ie(this))},H.prototype.forceUpdate=function(e){this.__v&&(this.__e=!0,e&&this.__h.push(e),ie(this))},H.prototype.render=X,U=[],Ie=typeof Promise=="function"?Promise.prototype.then.bind(Promise.resolve()):setTimeout,Oe=function(e,t){return e.__v.__b-t.__v.__b},V.__r=0,Ee=/(PointerCapture)$|Capture$/i,ce=0,te=ye(!1),ne=ye(!0),Me=0;var it=0;function l(e,t,n,i,r,o){t||(t={});var c,a,p=t;if("ref"in p)for(a in p={},t)a=="ref"?c=t[a]:p[a]=t[a];var _={type:e,props:p,key:n,ref:c,__k:null,__:null,__b:0,__e:null,__c:null,constructor:void 0,__v:--it,__i:-1,__u:0,__source:r,__self:o};if(typeof e=="function"&&(c=e.defaultProps))for(a in c)p[a]===void 0&&(p[a]=c[a]);return b.vnode&&b.vnode(_),_}var F,k,Q,be,L=0,Ge=[],S=b,ke=S.__b,Se=S.__r,Ce=S.diffed,$e=S.__c,Pe=S.unmount,we=S.__;function he(e,t){S.__h&&S.__h(k,e,L||t),L=0;var n=k.__H||(k.__H={__:[],__h:[]});return e>=n.__.length&&n.__.push({}),n.__[e]}function T(e){return L=1,rt(Be,e)}function rt(e,t,n){var i=he(F++,2);if(i.t=e,!i.__c&&(i.__=[Be(void 0,t),function(a){var p=i.__N?i.__N[0]:i.__[0],_=i.t(p,a);p!==_&&(i.__N=[_,i.__[1]],i.__c.setState({}))}],i.__c=k,!k.__f)){var r=function(a,p,_){if(!i.__c.__H)return!0;var v=i.__c.__H.__.filter(function(u){return!!u.__c});if(v.every(function(u){return!u.__N}))return!o||o.call(this,a,p,_);var s=i.__c.props!==a;return v.forEach(function(u){if(u.__N){var h=u.__[0];u.__=u.__N,u.__N=void 0,h!==u.__[0]&&(s=!0)}}),o&&o.call(this,a,p,_)||s};k.__f=!0;var o=k.shouldComponentUpdate,c=k.componentWillUpdate;k.componentWillUpdate=function(a,p,_){if(this.__e){var v=o;o=void 0,r(a,p,_),o=v}c&&c.call(this,a,p,_)},k.shouldComponentUpdate=r}return i.__N||i.__}function Y(e,t){var n=he(F++,3);!S.__s&&je(n.__H,t)&&(n.__=e,n.u=t,k.__H.__h.push(n))}function ot(e){return L=5,qe(function(){return{current:e}},[])}function qe(e,t){var n=he(F++,7);return je(n.__H,t)&&(n.__=e(),n.__H=t,n.__h=e),n.__}function $(e,t){return L=8,qe(function(){return e},t)}function lt(){for(var e;e=Ge.shift();)if(e.__P&&e.__H)try{e.__H.__h.forEach(K),e.__H.__h.forEach(le),e.__H.__h=[]}catch(t){e.__H.__h=[],S.__e(t,e.__v)}}S.__b=function(e){k=null,ke&&ke(e)},S.__=function(e,t){e&&t.__k&&t.__k.__m&&(e.__m=t.__k.__m),we&&we(e,t)},S.__r=function(e){Se&&Se(e),F=0;var t=(k=e.__c).__H;t&&(Q===k?(t.__h=[],k.__h=[],t.__.forEach(function(n){n.__N&&(n.__=n.__N),n.u=n.__N=void 0})):(t.__h.forEach(K),t.__h.forEach(le),t.__h=[],F=0)),Q=k},S.diffed=function(e){Ce&&Ce(e);var t=e.__c;t&&t.__H&&(t.__H.__h.length&&(Ge.push(t)!==1&&be===S.requestAnimationFrame||((be=S.requestAnimationFrame)||st)(lt)),t.__H.__.forEach(function(n){n.u&&(n.__H=n.u),n.u=void 0})),Q=k=null},S.__c=function(e,t){t.some(function(n){try{n.__h.forEach(K),n.__h=n.__h.filter(function(i){return!i.__||le(i)})}catch(i){t.some(function(r){r.__h&&(r.__h=[])}),t=[],S.__e(i,n.__v)}}),$e&&$e(e,t)},S.unmount=function(e){Pe&&Pe(e);var t,n=e.__c;n&&n.__H&&(n.__H.__.forEach(function(i){try{K(i)}catch(r){t=r}}),n.__H=void 0,t&&S.__e(t,n.__v))};var Te=typeof requestAnimationFrame=="function";function st(e){var t,n=function(){clearTimeout(i),Te&&cancelAnimationFrame(t),setTimeout(e)},i=setTimeout(n,35);Te&&(t=requestAnimationFrame(n))}function K(e){var t=k,n=e.__c;typeof n=="function"&&(e.__c=void 0,n()),k=t}function le(e){var t=k;e.__c=e.__(),k=t}function je(e,t){return!e||e.length!==t.length||t.some(function(n,i){return n!==e[i]})}function Be(e,t){return typeof t=="function"?t(e):t}var ct={};function z(e,t){for(var n in t)e[n]=t[n];return e}function at(e,t,n){var i,r=/(?:\?([^#]*))?(#.*)?$/,o=e.match(r),c={};if(o&&o[1])for(var a=o[1].split("&"),p=0;p<a.length;p++){var _=a[p].split("=");c[decodeURIComponent(_[0])]=decodeURIComponent(_.slice(1).join("="))}e=se(e.replace(r,"")),t=se(t||"");for(var v=Math.max(e.length,t.length),s=0;s<v;s++)if(t[s]&&t[s].charAt(0)===":"){var u=t[s].replace(/(^:|[+*?]+$)/g,""),h=(t[s].match(/[+*?]+$/)||ct)[0]||"",m=~h.indexOf("+"),d=~h.indexOf("*"),f=e[s]||"";if(!f&&!d&&(h.indexOf("?")<0||m)){i=!1;break}if(c[u]=decodeURIComponent(f),m||d){c[u]=e.slice(s).map(decodeURIComponent).join("/");break}}else if(t[s]!==e[s]){i=!1;break}return(n.default===!0||i!==!1)&&c}function _t(e,t){return e.rank<t.rank?1:e.rank>t.rank?-1:e.index-t.index}function ut(e,t){return e.index=t,e.rank=(function(n){return n.props.default?0:se(n.props.path).map(dt).join("")})(e),e.props}function se(e){return e.replace(/(^\/+|\/+$)/g,"").split("/")}function dt(e){return e.charAt(0)==":"?1+"*+?".indexOf(e.charAt(e.length-1))||4:5}var ht={},I=[],Ae=[],C=null,ze={url:pe()},pt=nt(ze);function pe(){var e;return""+((e=C&&C.location?C.location:C&&C.getCurrentLocation?C.getCurrentLocation():typeof location<"u"?location:ht).pathname||"")+(e.search||"")}function ft(e,t){return t===void 0&&(t=!1),typeof e!="string"&&e.url&&(t=e.replace,e=e.url),(function(n){for(var i=I.length;i--;)if(I[i].canRoute(n))return!0;return!1})(e)&&(function(n,i){i===void 0&&(i="push"),C&&C[i]?C[i](n):typeof history<"u"&&history[i+"State"]&&history[i+"State"](null,null,n)})(e,t?"replace":"push"),Ke(e)}function Ke(e){for(var t=!1,n=0;n<I.length;n++)I[n].routeTo(e)&&(t=!0);return t}function gt(e){if(e&&e.getAttribute){var t=e.getAttribute("href"),n=e.getAttribute("target");if(t&&t.match(/^\//g)&&(!n||n.match(/^_?self$/i)))return ft(t)}}function vt(e){return e.stopImmediatePropagation&&e.stopImmediatePropagation(),e.stopPropagation&&e.stopPropagation(),e.preventDefault(),!1}function mt(e){if(!(e.ctrlKey||e.metaKey||e.altKey||e.shiftKey||e.button)){var t=e.target;do if(t.localName==="a"&&t.getAttribute("href")){if(t.hasAttribute("data-native")||t.hasAttribute("native"))return;if(gt(t))return vt(e)}while(t=t.parentNode)}}var xe=!1;function Ve(e){e.history&&(C=e.history),this.state={url:e.url||pe()}}z(Ve.prototype=new H,{shouldComponentUpdate:function(e){return e.static!==!0||e.url!==this.props.url||e.onChange!==this.props.onChange},canRoute:function(e){var t=re(this.props.children);return this.g(t,e)!==void 0},routeTo:function(e){this.setState({url:e});var t=this.canRoute(e);return this.p||this.forceUpdate(),t},componentWillMount:function(){this.p=!0},componentDidMount:function(){var e=this;xe||(xe=!0,C||addEventListener("popstate",function(){Ke(pe())}),addEventListener("click",mt)),I.push(this),C&&(this.u=C.listen(function(t){var n=t.location||t;e.routeTo(""+(n.pathname||"")+(n.search||""))})),this.p=!1},componentWillUnmount:function(){typeof this.u=="function"&&this.u(),I.splice(I.indexOf(this),1)},componentWillUpdate:function(){this.p=!0},componentDidUpdate:function(){this.p=!1},g:function(e,t){e=e.filter(ut).sort(_t);for(var n=0;n<e.length;n++){var i=e[n],r=at(t,i.props.path,i.props);if(r)return[i,r]}},render:function(e,t){var n,i,r=e.onChange,o=t.url,c=this.c,a=this.g(re(e.children),o);if(a&&(i=tt(a[0],z(z({url:o,matches:n=a[1]},n),{key:void 0,ref:void 0}))),o!==(c&&c.url)){z(ze,c=this.c={url:o,previous:c&&c.url,current:i,path:i?i.props.path:null,matches:n}),c.router=this,c.active=i?[i]:[];for(var p=Ae.length;p--;)Ae[p]({});typeof r=="function"&&r(c)}return _e(pt.Provider,{value:c},i)}});var Z=function(e){return _e(e.component,e)};const yt="http://localhost:3000";class bt{async request(t,n={}){const i=`${yt}${t}`,r={headers:{"Content-Type":"application/json",...n.headers},...n};try{const o=await fetch(i,r);if(!o.ok)throw new Error(`HTTP error! status: ${o.status}`);return await o.json()}catch(o){throw console.error(`API request failed: ${t}`,o),o}}async healthCheck(){return this.request("/api/health")}async simulateCircuit(t){return this.request("/api/circuit/simulate",{method:"POST",body:JSON.stringify(t)})}async executeAlgorithm(t){return this.request("/api/algorithm/execute",{method:"POST",body:JSON.stringify(t)})}}const fe=new bt,kt={simulate:e=>fe.simulateCircuit(e)},St={execute:e=>fe.executeAlgorithm(e)},Ct=[{type:"AND",label:"与门",color:"#4CAF50"},{type:"OR",label:"或门",color:"#2196F3"},{type:"NOT",label:"非门",color:"#f44336"},{type:"XOR",label:"异或门",color:"#FF9800"},{type:"INPUT",label:"输入",color:"#9C27B0"},{type:"OUTPUT",label:"输出",color:"#607D8B"}];function $t(){const e=ot(null),[t,n]=T({gates:[],connections:[],inputs:{},outputs:{},simulationResult:null}),[i,r]=T({selectedTool:"select",selectedGateType:null,isSimulating:!1,simulationSpeed:1,zoom:1,panOffset:{x:0,y:0}}),[o,c]=T({isDragging:!1,draggedElement:null,dragOffset:{x:0,y:0},dragStart:{x:0,y:0}});Y(()=>{const u=e.current;if(!u)return;const h=u.getContext("2d");if(!h)return;u.width=u.offsetWidth,u.height=u.offsetHeight;let m;const d=()=>{wt(h,u,t),m=requestAnimationFrame(d)};return d(),()=>cancelAnimationFrame(m)},[t,i]);const a=$(u=>{const h=e.current;if(!h)return;const m=h.getBoundingClientRect(),d=u.clientX-m.left,f=u.clientY-m.top;if(i.selectedTool==="gate"&&i.selectedGateType){const g={id:`gate_${Date.now()}`,type:i.selectedGateType,position:{x:d,y:f}};n(y=>({...y,gates:[...y.gates,g]}))}else{const g=Pt(d,f,t);g&&c({isDragging:!0,draggedElement:g,dragOffset:{x:d-g.bounds.x,y:f-g.bounds.y},dragStart:{x:d,y:f}})}},[i,t]),p=$(u=>{const h=e.current;if(!h||!o.isDragging||!o.draggedElement)return;const m=h.getBoundingClientRect(),d=u.clientX-m.left,f=u.clientY-m.top;if(o.draggedElement.type==="gate"){const g=t.gates.find(y=>y.id===o.draggedElement.data.id);g&&(g.position={x:d-o.dragOffset.x,y:f-o.dragOffset.y},n(y=>({...y})))}},[o,t]),_=$(()=>{c({isDragging:!1,draggedElement:null,dragOffset:{x:0,y:0},dragStart:{x:0,y:0}})},[]),v=$(async()=>{r(u=>({...u,isSimulating:!0}));try{const u={gates:t.gates,connections:t.connections,inputs:t.inputs},h=await kt.simulate(u);n(m=>({...m,outputs:h.outputs,simulationResult:h}))}catch(u){console.error("Simulation failed:",u)}finally{r(u=>({...u,isSimulating:!1}))}},[t]),s=$(()=>{n({gates:[],connections:[],inputs:{},outputs:{},simulationResult:null})},[]);return l("div",{class:"circuit-designer",children:[l("div",{class:"circuit-toolbar",children:[l("div",{class:"tool-group",children:[l("button",{class:`tool-btn ${i.selectedTool==="select"?"active":""}`,onClick:()=>r(u=>({...u,selectedTool:"select",selectedGateType:null})),children:"选择"}),l("button",{class:`tool-btn ${i.selectedTool==="wire"?"active":""}`,onClick:()=>r(u=>({...u,selectedTool:"wire",selectedGateType:null})),children:"连接"}),l("button",{class:`tool-btn ${i.selectedTool==="delete"?"active":""}`,onClick:()=>r(u=>({...u,selectedTool:"delete",selectedGateType:null})),children:"删除"})]}),l("div",{class:"tool-group",children:Ct.map(u=>l("button",{class:`gate-btn ${i.selectedGateType===u.type?"active":""}`,style:{backgroundColor:u.color},onClick:()=>r(h=>({...h,selectedTool:"gate",selectedGateType:u.type})),children:u.label},u.type))}),l("div",{class:"tool-group",children:[l("button",{class:"action-btn simulate-btn",onClick:v,disabled:i.isSimulating,children:i.isSimulating?"模拟中...":"运行模拟"}),l("button",{class:"action-btn reset-btn",onClick:s,children:"重置"})]})]}),l("div",{class:"circuit-workspace",children:[l("canvas",{ref:e,class:"circuit-canvas",onMouseDown:a,onMouseMove:p,onMouseUp:_,onMouseLeave:_}),t.simulationResult&&l("div",{class:"simulation-results",children:[l("h3",{children:"模拟结果"}),l("div",{class:"signal-paths",children:t.simulationResult.signalPath.map((u,h)=>l("div",{class:"signal-step",children:[l("span",{class:"gate-id",children:u.gateId}),l("span",{class:"input-values",children:["输入: ",Object.entries(u.input).map(([m,d])=>`${m}=${d}`).join(", ")]}),l("span",{class:"output-value",children:["输出: ",u.output?"1":"0"]})]},h))})]})]})]})}function Pt(e,t,n){for(const i of n.gates){const r={x:i.position.x-40,y:i.position.y-25,width:80,height:50};if(e>=r.x&&e<=r.x+r.width&&t>=r.y&&t<=r.y+r.height)return{type:"gate",data:i,bounds:r}}return null}function wt(e,t,n){e.clearRect(0,0,t.width,t.height),Tt(e,t.width,t.height),n.connections.forEach(i=>{At(e,i,n.gates)}),n.gates.forEach(i=>{xt(e,i,n.outputs[i.id])})}function Tt(e,t,n){e.strokeStyle="#e0e0e0",e.lineWidth=.5;for(let i=0;i<t;i+=20)e.beginPath(),e.moveTo(i,0),e.lineTo(i,n),e.stroke();for(let i=0;i<n;i+=20)e.beginPath(),e.moveTo(0,i),e.lineTo(t,i),e.stroke()}function At(e,t,n){const i=n.find(a=>a.id===t.from),r=n.find(a=>a.id===t.to);if(!i||!r)return;e.strokeStyle="#666",e.lineWidth=2,e.beginPath(),e.moveTo(i.position.x+40,i.position.y),e.lineTo(r.position.x-40,r.position.y),e.stroke();const o=Math.atan2(r.position.y-i.position.y,r.position.x-i.position.x),c=10;e.beginPath(),e.moveTo(r.position.x-40,r.position.y),e.lineTo(r.position.x-40-c*Math.cos(o-Math.PI/6),r.position.y-c*Math.sin(o-Math.PI/6)),e.moveTo(r.position.x-40,r.position.y),e.lineTo(r.position.x-40-c*Math.cos(o+Math.PI/6),r.position.y-c*Math.sin(o+Math.PI/6)),e.stroke()}function xt(e,t,n){const{x:i,y:r}=t.position,o=80,c=50;e.fillStyle=Ut(t.type),e.fillRect(i-o/2,r-c/2,o,c),e.fillStyle="white",e.font="bold 14px Arial",e.textAlign="center",e.fillText(t.type,i,r+5),n!==void 0&&(e.fillStyle=n?"#4CAF50":"#f44336",e.beginPath(),e.arc(i+o/2,r,5,0,2*Math.PI),e.fill())}function Ut(e){switch(e){case"AND":return"#4CAF50";case"OR":return"#2196F3";case"NOT":return"#f44336";case"XOR":return"#FF9800";case"INPUT":return"#9C27B0";case"OUTPUT":return"#607D8B";default:return"#666"}}const ee=[{value:"addition",label:"加法算法",icon:"➕"},{value:"multiplication",label:"乘法算法",icon:"✖️"},{value:"binary_addition",label:"二进制加法",icon:"🔢"}];function It(){const[e,t]=T({currentStep:-1,isPlaying:!1,speed:1,steps:[]}),[n,i]=T({algorithm:"addition",operands:[5,3]}),[r,o]=T(!1),[c,a]=T("5,3");Y(()=>{if(e.isPlaying&&e.currentStep<e.steps.length-1){const d=setTimeout(()=>{t(f=>({...f,currentStep:f.currentStep+1}))},1e3/e.speed);return()=>clearTimeout(d)}else e.currentStep>=e.steps.length-1&&t(d=>({...d,isPlaying:!1}))},[e.currentStep,e.isPlaying,e.speed,e.steps.length]);const p=$(async()=>{o(!0);try{const d=await St.execute(n);t(f=>({...f,steps:d.steps,currentStep:-1,isPlaying:!1}))}catch(d){console.error("Algorithm execution failed:",d)}finally{o(!1)}},[n]),_=$(d=>{a(d);const f=d.split(",").map(g=>parseInt(g.trim())).filter(g=>!isNaN(g));f.length>0&&i(g=>({...g,operands:f}))},[]),v=$(()=>{e.steps.length===0?p():t(d=>({...d,isPlaying:!d.isPlaying,currentStep:d.isPlaying?d.currentStep:-1}))},[e,p]),s=$(()=>{t(d=>({...d,currentStep:-1,isPlaying:!1}))},[]),u=$(()=>{e.currentStep<e.steps.length-1&&t(d=>({...d,currentStep:d.currentStep+1}))},[e.currentStep,e.steps.length]),h=$(()=>{e.currentStep>-1&&t(d=>({...d,currentStep:d.currentStep-1}))},[e.currentStep]),m=e.currentStep>=0?e.steps[e.currentStep]:null;return l("div",{class:"algorithm-visualizer",children:[l("div",{class:"visualizer-header",children:[l("h2",{children:"算法可视化器"}),l("p",{class:"visualizer-description",children:"选择一个算法，输入操作数，然后逐步观察算法的执行过程"})]}),l("div",{class:"algorithm-controls",children:[l("div",{class:"control-group",children:[l("label",{children:"选择算法:"}),l("select",{value:n.algorithm,onChange:d=>i(f=>({...f,algorithm:d.target.value})),children:ee.map(d=>l("option",{value:d.value,children:[d.icon," ",d.label]},d.value))})]}),l("div",{class:"control-group",children:[l("label",{children:"输入操作数 (逗号分隔):"}),l("input",{type:"text",value:c,onInput:d=>_(d.target.value),placeholder:"例如: 5,3"})]}),l("div",{class:"control-group",children:[l("label",{children:"动画速度:"}),l("input",{type:"range",min:"0.5",max:"3",step:"0.5",value:e.speed,onInput:d=>t(f=>({...f,speed:parseFloat(d.target.value)}))}),l("span",{children:[e.speed,"x"]})]})]}),l("div",{class:"algorithm-workspace",children:[l("div",{class:"visualization-area",children:[l("div",{class:"algorithm-display",children:[l("div",{class:"algorithm-title",children:[l("h3",{children:[ee.find(d=>d.value===n.algorithm)?.icon," ",ee.find(d=>d.value===n.algorithm)?.label]}),l("div",{class:"operands",children:["操作数: ",n.operands.join(" 和 ")]})]}),m&&l("div",{class:"current-step",children:[l("div",{class:"step-header",children:[l("span",{class:"step-number",children:["步骤 ",m.step]}),l("div",{class:"step-progress",children:[e.currentStep+1," / ",e.steps.length]})]}),l("div",{class:"step-description",children:m.description}),l("div",{class:"registers",children:[l("h4",{children:"寄存器状态:"}),l("div",{class:"register-grid",children:Object.entries(m.registers).map(([d,f])=>l("div",{class:"register-item",children:[l("span",{class:"register-name",children:[d,":"]}),l("span",{class:"register-value",children:f})]},d))})]})]}),e.steps.length===0&&!r&&l("div",{class:"empty-state",children:[l("div",{class:"empty-icon",children:"⚡"}),l("h3",{children:"准备开始"}),l("p",{children:"点击运行按钮来执行算法并查看详细步骤"})]}),r&&l("div",{class:"loading-state",children:[l("div",{class:"loading-spinner"}),l("p",{children:"正在执行算法..."})]})]}),l("div",{class:"step-timeline",children:[l("h4",{children:"执行时间线"}),l("div",{class:"timeline",children:e.steps.map((d,f)=>l("div",{class:`timeline-item ${f===e.currentStep?"active":""} ${f<e.currentStep?"completed":""}`,onClick:()=>t(g=>({...g,currentStep:f})),children:[l("div",{class:"timeline-marker"}),l("div",{class:"timeline-content",children:[l("div",{class:"timeline-step",children:["步骤 ",d.step]}),l("div",{class:"timeline-desc",children:d.description})]})]},f))})]})]}),l("div",{class:"playback-controls",children:[l("button",{class:"control-btn reset-btn",onClick:s,disabled:r,children:"🔄 重置"}),l("button",{class:"control-btn prev-btn",onClick:h,disabled:e.currentStep<=-1||r,children:"⏮️ 上一步"}),l("button",{class:"control-btn play-btn",onClick:v,disabled:r,children:e.isPlaying?"⏸️ 暂停":"▶️ 运行"}),l("button",{class:"control-btn next-btn",onClick:u,disabled:e.currentStep>=e.steps.length-1||r,children:"⏭️ 下一步"})]})]})]})}function Ot(){const[e,t]=T(0);Y(()=>{const r=setInterval(()=>{t(o=>(o+1)%4)},2e3);return()=>clearInterval(r)},[]);const n=[{title:"交互式电路模拟",description:"通过拖拽和连接逻辑门，实时观察信号在电路中的流动路径",icon:"🔌",path:"/circuit",color:"#4CAF50"},{title:"算法动态可视化",description:"以动画形式展示经典运算算法的每一步执行过程和寄存器变化",icon:"⚡",path:"/algorithm",color:"#2196F3"}],i=[{title:"半加器电路",description:"学习最基本的加法电路设计",type:"circuit"},{title:"二进制加法算法",description:"理解计算机如何进行数字运算",type:"algorithm"},{title:"全加器电路",description:"构建支持进位的加法电路",type:"circuit"},{title:"乘法算法",description:"探索高效的乘法运算方法",type:"algorithm"}];return l("div",{class:"home",children:[l("section",{class:"hero-section",children:l("div",{class:"container",children:[l("div",{class:"hero-content",children:[l("h1",{class:"hero-title",children:["数芯之光",l("span",{class:"hero-subtitle",children:"Digital Circuit & Algorithm Visualizer"})]}),l("p",{class:"hero-description",children:"一个交互式的数字电路模拟器和算法可视化平台，让抽象的计算过程变得直观易懂。 通过可视化的方式探索计算机硬件和软件的奥秘。"}),l("div",{class:"hero-actions",children:[l("a",{href:"/circuit",class:"btn btn-primary",children:"开始电路设计"}),l("a",{href:"/algorithm",class:"btn btn-secondary",children:"探索算法"})]})]}),l("div",{class:"hero-visual",children:l("div",{class:"circuit-preview",children:[l("div",{class:"animated-gate","data-step":e,children:[l("div",{class:"gate and-gate",children:"AND"}),l("div",{class:"gate or-gate",children:"OR"}),l("div",{class:"gate xor-gate",children:"XOR"}),l("div",{class:"gate not-gate",children:"NOT"})]}),l("div",{class:"signal-flow","data-active":e>0,children:[l("div",{class:"signal-path"}),l("div",{class:"signal-path"})]})]})})]})}),l("section",{class:"features-section",children:l("div",{class:"container",children:[l("h2",{class:"section-title",children:"核心功能"}),l("div",{class:"features-grid",children:n.map(r=>l("div",{class:"feature-card",children:[l("div",{class:"feature-icon",style:{backgroundColor:r.color},children:r.icon}),l("h3",{class:"feature-title",children:r.title}),l("p",{class:"feature-description",children:r.description}),l("a",{href:r.path,class:"feature-link",children:"立即体验 →"})]},r.title))})]})}),l("section",{class:"examples-section",children:l("div",{class:"container",children:[l("h2",{class:"section-title",children:"经典示例"}),l("div",{class:"examples-grid",children:i.map(r=>l("div",{class:"example-card",children:[l("div",{class:"example-type",children:[r.type==="circuit"?"🔌":"⚡"," ",r.type==="circuit"?"电路":"算法"]}),l("h3",{class:"example-title",children:r.title}),l("p",{class:"example-description",children:r.description})]},r.title))})]})}),l("section",{class:"cta-section",children:l("div",{class:"container",children:[l("h2",{class:"cta-title",children:"准备好探索数字世界了吗？"}),l("p",{class:"cta-description",children:"从简单的逻辑门开始，逐步构建复杂的数字系统。让每个信号的流动都清晰可见！"}),l("div",{class:"cta-actions",children:[l("a",{href:"/circuit",class:"btn btn-primary btn-large",children:"开始设计电路"}),l("a",{href:"/algorithm",class:"btn btn-secondary btn-large",children:"查看算法演示"})]})]})})]})}function Et(){const[e,t]=T("/"),[n,i]=T(!1);return Y(()=>{fe.healthCheck().then(o=>{i(!0),console.log("API 连接正常:",o.message)}).catch(o=>{i(!1),console.error("API 连接失败:",o)})},[]),l("div",{class:"app",children:[l("header",{class:"app-header",children:l("div",{class:"container",children:[l("h1",{class:"app-title",children:"数芯之光"}),l("nav",{class:"app-nav",children:[l("a",{href:"/",class:`nav-link ${e==="/"?"active":""}`,children:"首页"}),l("a",{href:"/circuit",class:`nav-link ${e==="/circuit"?"active":""}`,children:"电路模拟器"}),l("a",{href:"/algorithm",class:`nav-link ${e==="/algorithm"?"active":""}`,children:"算法可视化"})]}),l("div",{class:"api-status",children:[l("span",{class:`status-indicator ${n?"healthy":"unhealthy"}`}),"API ",n?"正常":"离线"]})]})}),l("main",{class:"app-main",children:l(Ve,{onChange:o=>{t(o.url)},children:[l(Z,{path:"/",component:Ot}),l(Z,{path:"/circuit",component:$t}),l(Z,{path:"/algorithm",component:It})]})}),l("footer",{class:"app-footer",children:l("div",{class:"container",children:l("p",{children:"© 2024 数芯之光 - 交互式数字电路与算法可视化系统"})})})]})}et(l(Et,{}),document.getElementById("app"));
